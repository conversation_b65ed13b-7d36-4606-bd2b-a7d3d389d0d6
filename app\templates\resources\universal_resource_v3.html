{% extends "base.html" %}

{% block title %}{{ page_title }} - AEC-FT ICP{% endblock %}

{% block extra_css %}
<!-- 引入APS主题样式 -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/custom/theme.css') }}">
<style>


.table-responsive {
    max-height: 75vh;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 4px;
}

/* Excel上传按钮 */
.excel-upload-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    transition: all 0.2s;
}

.excel-upload-btn:hover {
    background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
    transform: translateY(-1px);
    color: white;
}

.table th {
    white-space: nowrap;
    min-width: 80px;
    max-width: 200px;
    position: sticky;
    top: 0;
    background-color: #f8f9fa;
    z-index: 1;
    padding: 6px 8px;
    font-size: 0.875rem;
    font-weight: 600;
}

.table td {
    white-space: nowrap;
    padding: 4px 8px;
    font-size: 0.875rem;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.table-sm th,
.table-sm td {
    padding: 4px 6px;
}

/* 紧凑型按钮 */
.btn-sm {
    padding: 2px 6px;
    font-size: 0.75rem;
}

/* 紧凑型表单控件 */
.form-control-sm, .form-select-sm {
    padding: 2px 6px;
    font-size: 0.875rem;
}

/* 操作列固定宽度 */
.action-column {
    width: 140px;
    min-width: 140px;
    max-width: 140px;
}

/* 选择列固定宽度 */
.select-column {
    width: 35px;
    min-width: 35px;
    max-width: 35px;
    text-align: center;
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.filter-row {
    display: flex;
    gap: 10px;
    align-items: end;
    margin-bottom: 10px;
}

.filter-field, .filter-operator, .filter-value {
    flex: 1;
}

.filter-actions {
    flex: 0 0 auto;
}

/* 卡片头部样式 */
.card-header {
    background-color: var(--aps-primary);
    color: white;
}



/* 业务键字段高亮 */
.business-key-col {
    background-color: #fff3cd !important;
    font-weight: bold;
}

/* 只读字段样式 */
.readonly-col {
    background-color: #f8f9fa !important;
    color: #6c757d;
}

/* 日期时间字段样式 */
.datetime-col {
    background-color: #e7f3ff !important;
}

/* =========================
   新增: 调整模式专用样式
   ========================= */
.mode-toggle {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    border: none;
    border-radius: 20px;
    padding: 6px 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.mode-toggle .form-check-input {
    margin-right: 8px;
}

.mode-toggle .form-check-label {
    color: white;
    font-weight: 500;
    margin-right: 15px;
}

.mode-toggle .mode-indicator {
    position: absolute;
    top: 50%;
    right: 15px;
    transform: translateY(-50%);
    font-size: 0.8rem;
    color: #ffffff;
    opacity: 0.8;
}

.adjustment-interface {
    display: none;
    min-height: 600px;
}

.adjustment-interface.active {
    display: block;
}

/* 确保隐藏类优先级 */
.adjustment-interface.hidden {
    display: none !important;
}

.handler-group {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 2px solid #e9ecef;
    border-radius: 12px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.handler-group:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.1);
}

.handler-group.drag-over {
    border-color: #dc3545;
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    transform: scale(1.02);
}

.handler-header {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    padding: 15px 20px;
    border-radius: 10px 10px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.handler-info h5 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.handler-info .handler-id {
    font-size: 0.9rem;
    opacity: 0.9;
    margin-top: 2px;
}

.handler-stats {
    display: flex;
    gap: 15px;
    align-items: center;
}

.stat-item {
    text-align: center;
    min-width: 60px;
}

.stat-value {
    font-size: 1.2rem;
    font-weight: bold;
    display: block;
}

.stat-label {
    font-size: 0.8rem;
    opacity: 0.9;
}

.load-indicator {
    width: 80px;
    height: 8px;
    background: rgba(255,255,255,0.3);
    border-radius: 4px;
    overflow: hidden;
    margin-top: 5px;
}

.load-bar {
    height: 100%;
    background: linear-gradient(90deg, #28a745 0%, #ffc107 70%, #dc3545 100%);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.batch-container {
    padding: 15px;
    min-height: 120px;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    margin-top: 10px;
    background: #fafafa;
    position: relative;
    transition: all 0.3s ease;
}

.batch-container.drag-over {
    border-color: #007bff;
    background: #e7f3ff;
}

.batch-container.empty {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    font-style: italic;
}

.batch-card {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
    cursor: move;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.batch-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.batch-card.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

.batch-card.drag-preview {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border-color: #007bff;
}

.batch-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.batch-priority {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: bold;
    min-width: 50px;
    text-align: center;
}

.batch-info {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    font-size: 0.9rem;
}

.batch-info-item {
    background: #f8f9fa;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
}

.batch-actions {
    display: flex;
    gap: 5px;
    margin-top: 8px;
}

.batch-actions .btn {
    padding: 2px 6px;
    font-size: 0.7rem;
}

.adjustment-controls {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    display: flex;
    gap: 10px;
}

.floating-btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: none;
    color: white;
    font-size: 1.2rem;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    transition: all 0.3s ease;
}

.floating-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.4);
}

.floating-btn.save {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.floating-btn.cancel {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

.floating-btn.undo {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
}

.adjustment-summary {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.summary-stats {
    display: flex;
    justify-content: space-around;
    text-align: center;
}

.summary-stat {
    flex: 1;
}

.summary-value {
    font-size: 1.5rem;
    font-weight: bold;
    display: block;
}

.summary-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .handler-stats {
        flex-direction: column;
        gap: 8px;
    }
    
    .stat-item {
        min-width: auto;
    }
    
    .batch-info {
        flex-direction: column;
        gap: 5px;
    }
    
    .adjustment-controls {
        bottom: 10px;
        right: 10px;
    }
    
    .floating-btn {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
}

/* 拖拽排序动画 */
@keyframes dragFeedback {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.batch-card.drag-feedback {
    animation: dragFeedback 0.3s ease;
}

/* Toast提示样式 */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1050;
}

.toast {
    min-width: 300px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.toast-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border-bottom: none;
}

.toast-body {
    padding: 15px;
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 原有界面隐藏类 */
.view-mode-content {
    display: block;
}

.view-mode-content.hidden {
    display: none;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 注意：lotprioritydone表的调整功能在 /production/done-lots 页面实现 -->

    <!-- 原有查看模式界面 -->
    <div class="view-mode-content" id="viewModeContent">
        <div class="row mb-3">
            <div class="col">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="card-title mb-0">{{ page_title }}</h5>
                            <div>
                                <!-- Excel导入按钮 - 仅在优先级配置表显示 -->
                                <button type="button" class="btn excel-upload-btn me-2" id="excelImportBtn" onclick="showExcelUploadModal()" style="display: none;">
                                    <i class="fas fa-file-excel me-1"></i>Excel导入
                                </button>
                                <!-- 同步数据表隐藏新增按钮 -->
                                <button type="button" class="btn btn-success me-2" onclick="addRecord()" {% if table_name in ['wip_lot', 'eqp_status', 'et_ft_test_spec', 'tcc_inv', 'et_uph_eqp', 'ct', 'et_recipe_file', 'stage_mapping_config', 'et_wait_lot'] %}style="display: none;"{% endif %}>
                                    <i class="fas fa-plus me-1"></i>新增
                                </button>
                                <button type="button" class="btn btn-outline-primary me-2" onclick="refreshData()">
                                    <i class="fas fa-sync-alt me-1"></i>刷新
                                </button>
                                <!-- 分层导出下拉菜单 -->
                                <div class="btn-group me-2" role="group">
                                    <button type="button" class="btn btn-info dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="fas fa-file-excel me-1"></i>导出
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" onclick="exportCurrentPage()">
                                            <i class="fas fa-file-alt me-2"></i>导出当前页
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="exportFilteredData()">
                                            <i class="fas fa-filter me-2"></i>导出筛选结果
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="exportAllData()">
                                            <i class="fas fa-database me-2"></i>导出全部数据
                                        </a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <!-- 高级筛选面板 -->
                        <div class="mb-3">
                            <h6>
                                <i class="fas fa-filter me-2"></i>高级筛选
                                <small class="text-muted ms-2">支持多条件组合查询</small>
                            </h6>
                            
                            <!-- 筛选条件 -->
                            <div id="filterConditions">
                                <div class="filter-row" data-index="0">
                                    <div class="filter-field">
                                        <label class="form-label form-label-sm">字段</label>
                                        <select class="form-select form-select-sm" name="field">
                                            <option value="">请选择字段</option>
                                        </select>
                                    </div>
                                    <div class="filter-operator">
                                        <label class="form-label form-label-sm">操作符</label>
                                        <select class="form-select form-select-sm" name="operator">
                                            <option value="contains">包含</option>
                                            <option value="equals">等于</option>
                                            <option value="starts_with">开始于</option>
                                            <option value="ends_with">结束于</option>
                                            <option value="not_equals">不等于</option>
                                        </select>
                                    </div>
                                    <div class="filter-value">
                                        <label class="form-label form-label-sm">值</label>
                                        <input type="text" class="form-control form-control-sm" name="value" placeholder="输入筛选值">
                                    </div>
                                    <div class="filter-actions">
                                        <label class="form-label form-label-sm">&nbsp;</label>
                                        <div>
                                            <button type="button" class="btn btn-sm btn-success" onclick="addFilterCondition()" title="添加条件">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-danger" onclick="removeFilterCondition(0)" title="删除条件">
                                                <i class="fas fa-minus"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 筛选操作按钮 -->
                            <div class="d-flex justify-content-between align-items-center mt-2">
                                <div>
                                    <button type="button" class="btn btn-primary btn-sm" onclick="applyFilter()">
                                        <i class="fas fa-search me-1"></i>应用筛选
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm ms-2" onclick="clearFilter()">
                                        <i class="fas fa-times me-1"></i>清除筛选
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 数据预览区域 -->
                        <div class="preview-area">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0">{{ page_title }}数据预览</h6>
                                <div>
                                    <span class="badge bg-primary me-2" id="recordCount">0 条记录</span>
                                    <select class="form-select form-select-sm d-inline-block" style="width: auto;" id="pageSize" onchange="changePageSize()">
                                        <option value="25">25 条/页</option>
                                        <option value="50" selected>50 条/页</option>
                                        <option value="100">100 条/页</option>
                                        <option value="500">500 条/页</option>
                                        <option value="1000">1000 条/页</option>
                                        <option value="5000">5000 条/页</option>
                                        <option value="10000">显示全部</option>
                                    </select>
                                </div>
                            </div>
                            
                            <!-- 批量操作工具栏 -->
                            <div class="batch-operations mb-3" style="display: none;" id="batchOperations">
                                <div class="alert alert-info py-2">
                                    <span id="selectedCount">0</span> 条记录已选择
                                    <button type="button" class="btn btn-sm btn-outline-danger ms-3" onclick="batchDelete()">
                                        <i class="fas fa-trash me-1"></i>批量删除
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary ms-2" onclick="clearSelection()">
                                        <i class="fas fa-times me-1"></i>取消选择
                                    </button>
                                </div>
                            </div>
                            
                            <div class="table-responsive" style="position: relative;">
                                <div class="loading-overlay" id="loadingOverlay" style="display: none;">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                </div>
                                
                                <table class="table table-sm table-hover table-striped" id="dataTable">
                                    <thead class="table-light">
                                        <tr id="tableHeaders">
                                            <!-- 表头将动态生成 -->
                                        </tr>
                                    </thead>
                                    <tbody id="tableBody">
                                        <tr>
                                            <td colspan="20" class="text-center">加载中...</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- 分页导航 -->
                            <nav class="mt-3" aria-label="数据分页">
                                <ul class="pagination justify-content-center" id="pagination">
                                    <!-- 分页按钮将动态生成 -->
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

         <!-- 调整模式界面已迁移至 /production/done-lots 页面 -->
</div>

<!-- 新增/编辑模态框 -->
<div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editModalLabel">新增记录</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editForm">
                    <div id="formFields">
                        <!-- 表单字段将动态生成 -->
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveRecord()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除这条记录吗？此操作不可撤销。</p>
                <div id="deleteRecordInfo"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" onclick="confirmDelete()">确认删除</button>
            </div>
        </div>
    </div>
</div>

<!-- 批量删除确认模态框 -->
<div class="modal fade" id="batchDeleteModal" tabindex="-1" aria-labelledby="batchDeleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchDeleteModalLabel">确认批量删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除选中的 <span id="batchDeleteCount">0</span> 条记录吗？此操作不可撤销。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" onclick="confirmBatchDelete()">确认删除</button>
            </div>
        </div>
    </div>
</div>

<!-- Excel上传模态框 -->
<div class="modal fade" id="excelUploadModal" tabindex="-1" aria-labelledby="excelUploadModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="excelUploadModalLabel">Excel文件上传</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="excelFiles" class="form-label">选择Excel文件</label>
                    <input class="form-control" type="file" id="excelFiles" accept=".xlsx,.xls">
                    <div class="form-text" id="uploadHelpText">
                        仅支持单个Excel文件上传。
                    </div>
                </div>

                <div class="alert alert-info" id="formatInfo">
                    <h6><i class="fas fa-info-circle me-1"></i>文件格式说明</h6>
                    <div id="formatDetails">
                        <!-- 格式说明将根据表类型动态生成 -->
                    </div>
                </div>

                <div class="upload-progress" id="uploadProgress" style="display: none;">
                    <div class="progress">
                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                    <small class="text-muted" id="uploadStatus">准备上传...</small>
                </div>

                <div class="upload-result" id="uploadResult" style="display: none;"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="uploadExcelFiles()">开始上传</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- 统一导出系统依赖 -->
<script src="{{ url_for('static', filename='vendor/xlsx/xlsx.full.min.js') }}"></script>
<script src="{{ url_for('static', filename='js/unified-export.js') }}"></script>

<script>
// 全局变量
let currentPage = 1;
let pageSize = 50;
let totalPages = 1;
let totalRecords = 0;
let advancedFilters = [];
let filterConditionIndex = 0;
let availableFields = [];
let tableData = [];
let sortColumn = '';
let sortDirection = 'asc'; // 'asc' 或 'desc'
let currentColumns = [];

// 表名配置
const TABLE_NAME = '{{ table_name }}';
const API_BASE = '/api/v3';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log(`${TABLE_NAME} 页面加载完成，开始初始化数据...`);
    initializePage();
    checkExcelImportSupport();
    setupModeToggle(); // 初始化模式切换
});

// 初始化页面 - 优化为分页加载（非全量加载）
async function initializePage() {
    try {
        showLoading(true);
        
        // 1. 加载表结构
        await loadTableStructure();
        
        // 2. 自动加载第一页数据（使用分页查询，不是全量数据）
        await loadData();
        
        // 3. 绑定事件
        bindEvents();
        
        console.log('✅ 页面初始化完成 - 分页加载模式');
    } catch (error) {
        console.error('❌ 页面初始化失败:', error);
        
        // 显示错误状态
        document.getElementById('tableBody').innerHTML = `
            <tr>
                <td colspan="100%" class="text-center py-4 text-danger">
                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                    <div>页面初始化失败</div>
                    <div class="small">${error.message}</div>
                    <button class="btn btn-sm btn-outline-primary mt-2" onclick="location.reload()">
                        <i class="fas fa-redo me-1"></i>重新加载页面
                    </button>
                </td>
            </tr>
        `;
    } finally {
        showLoading(false);
    }
}



// 加载表结构
async function loadTableStructure() {
    try {
        console.log(`📋 正在加载表结构: ${TABLE_NAME}`);
        const response = await fetch(`${API_BASE}/tables/${TABLE_NAME}/structure?_t=${Date.now()}`, {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        });

        if (!response.ok) {
            if (response.status === 401) {
                throw new Error('用户未登录或会话已过期，请重新登录');
            } else if (response.status === 403) {
                throw new Error('没有权限访问此资源');
            } else if (response.status === 404) {
                throw new Error(`表 ${TABLE_NAME} 不存在`);
            } else {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
        }

        const result = await response.json();

        if (result.success) {
            currentColumns = result.columns || [];
            console.log(`✅ 表结构加载成功，字段数: ${currentColumns.length}`);
            setupFilterFields();
        } else {
            throw new Error(result.error || '获取表结构失败');
        }
    } catch (error) {
        console.error('❌ 加载表结构失败:', error);
        throw error;
    }
}

// 设置筛选字段选项
function setupFilterFields() {
    const filterField = document.getElementById('filterConditions').querySelector('select[name="field"]');
    if (!filterField) return;

    filterField.innerHTML = '<option value="">请选择字段</option>';

    currentColumns.forEach(column => {
        if (!column.hidden) {
            const option = document.createElement('option');
            option.value = column.name;
            option.textContent = column.display_name || column.name;
            filterField.appendChild(option);
        }
    });

    console.log(`✅ 筛选字段设置完成，可用字段: ${currentColumns.filter(c => !c.hidden).length}个`);
}

// 加载数据
async function loadData() {
    showLoading(true);

    try {
        console.log(`📊 正在加载数据: ${TABLE_NAME}, 页码: ${currentPage}`);

        // 构建查询参数
        const params = new URLSearchParams({
            page: currentPage,
            per_page: pageSize
        });

        // 添加排序参数
        if (sortColumn) {
            params.append('sort_by', sortColumn);
            params.append('sort_order', sortDirection);
        }

        // 添加筛选参数
        if (advancedFilters.length > 0) {
            params.append('filters', JSON.stringify(advancedFilters));
        }

        // 添加时间戳参数防止缓存
        params.append('_t', Date.now());

        const response = await fetch(`${API_BASE}/tables/${TABLE_NAME}/data?${params}`, {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();

        if (result.success) {
            tableData = result.data || [];
            totalRecords = result.total || 0;
            totalPages = Math.ceil(totalRecords / pageSize);

            console.log(`✅ 数据加载成功: ${tableData.length} 条记录`);

            renderTable();
            renderPagination();
            updateRecordCount();
        } else {
            throw new Error(result.error || '获取数据失败');
        }
    } catch (error) {
        console.error('❌ 加载数据失败:', error);
        showError('数据加载失败: ' + error.message);

        document.getElementById('tableBody').innerHTML = `
            <tr>
                <td colspan="100%" class="text-center py-4 text-danger">
                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                    <div>数据加载失败</div>
                    <div class="small">${error.message}</div>
                    <button class="btn btn-sm btn-outline-primary mt-2" onclick="loadData()">
                        <i class="fas fa-redo me-1"></i>重试
                    </button>
                </td>
            </tr>
        `;
    } finally {
        showLoading(false);
    }
}

// 渲染表格
function renderTable() {
    const tableHeaders = document.getElementById('tableHeaders');
    const tableBody = document.getElementById('tableBody');

    // 清空现有内容
    tableHeaders.innerHTML = '';
    tableBody.innerHTML = '';

    if (!currentColumns.length || !tableData.length) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="100%" class="text-center py-4 text-muted">
                    <i class="fas fa-inbox fa-2x mb-2"></i>
                    <div>暂无数据</div>
                </td>
            </tr>
        `;
        return;
    }

    // 渲染表头
    renderTableHeaders();

    // 渲染数据行
    renderTableRows();
}

// 渲染表头
function renderTableHeaders() {
    const tableHeaders = document.getElementById('tableHeaders');

    // 选择列
    const selectTh = document.createElement('th');
    selectTh.className = 'select-column';
    selectTh.innerHTML = `
        <input type="checkbox" class="form-check-input" id="selectAll" onchange="toggleSelectAll()">
    `;
    tableHeaders.appendChild(selectTh);

    // 数据列
    currentColumns.forEach(column => {
        if (!column.hidden) {
            const th = document.createElement('th');
            th.innerHTML = `
                <div class="d-flex align-items-center justify-content-between">
                    <span>${column.display_name || column.name}</span>
                    <div class="sort-icons">
                        <i class="fas fa-sort text-muted" style="cursor: pointer;" onclick="sortTable('${column.name}')"></i>
                    </div>
                </div>
            `;

            // 添加字段类型样式
            if (column.primary_key) {
                th.classList.add('readonly-col');
            } else if (column.type === 'datetime') {
                th.classList.add('datetime-col');
            }

            tableHeaders.appendChild(th);
        }
    });

    // 操作列
    const actionTh = document.createElement('th');
    actionTh.className = 'action-column';
    actionTh.textContent = '操作';
    tableHeaders.appendChild(actionTh);
}

// 渲染数据行
function renderTableRows() {
    const tableBody = document.getElementById('tableBody');

    tableData.forEach((row, index) => {
        const tr = document.createElement('tr');
        tr.setAttribute('data-row-index', index);

        // 选择列
        const selectTd = document.createElement('td');
        selectTd.className = 'select-column';
        selectTd.innerHTML = `
            <input type="checkbox" class="form-check-input row-select" value="${index}" onchange="updateBatchOperations()">
        `;
        tr.appendChild(selectTd);

        // 数据列
        currentColumns.forEach(column => {
            if (!column.hidden) {
                const td = document.createElement('td');
                const value = row[column.name] || '';

                // 格式化显示值
                td.innerHTML = formatCellValue(value, column);
                td.title = value; // 悬停显示完整内容

                // 添加字段类型样式
                if (column.primary_key) {
                    td.classList.add('readonly-col');
                } else if (column.type === 'datetime') {
                    td.classList.add('datetime-col');
                }

                tr.appendChild(td);
            }
        });

        // 操作列
        const actionTd = document.createElement('td');
        actionTd.className = 'action-column';
        actionTd.innerHTML = `
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-sm btn-outline-primary" onclick="editRecord(${index})" title="编辑">
                    <i class="fas fa-edit"></i>
                </button>
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteRecord(${index})" title="删除">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
        tr.appendChild(actionTd);

        tableBody.appendChild(tr);
    });
}

// 格式化单元格值
function formatCellValue(value, column) {
    if (!value) return '';

    // 根据字段类型格式化
    switch (column.type) {
        case 'status_badge':
            return formatStatusBadge(value);
        case 'datetime':
            return formatDateTime(value);
        default:
            return escapeHtml(value);
    }
}

// 格式化状态徽章
function formatStatusBadge(value) {
    const statusMap = {
        'ONLINE': { class: 'success', text: '在线' },
        'OFFLINE': { class: 'danger', text: '离线' },
        'MAINTENANCE': { class: 'warning', text: '维护中' },
        'ERROR': { class: 'danger', text: '故障' }
    };

    const status = statusMap[value] || { class: 'secondary', text: value };
    return `<span class="badge bg-${status.class}">${status.text}</span>`;
}

// 格式化日期时间
function formatDateTime(value) {
    if (!value) return '';
    try {
        const date = new Date(value);
        return date.toLocaleString('zh-CN');
    } catch (e) {
        return value;
    }
}

// HTML转义
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 显示/隐藏加载状态
function showLoading(show) {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.style.display = show ? 'flex' : 'none';
    }
}

// 显示成功消息
function showSuccess(message) {
    // 这里可以集成Toast组件或其他通知系统
    console.log('✅ ' + message);
    alert('成功: ' + message);
}

// 显示错误消息
function showError(message) {
    // 这里可以集成Toast组件或其他通知系统
    console.error('❌ ' + message);
    alert('错误: ' + message);
}

// 显示信息消息
function showInfo(message) {
    // 这里可以集成Toast组件或其他通知系统
    console.info('ℹ️ ' + message);
    alert('信息: ' + message);
}

// 绑定事件
function bindEvents() {
    // 绑定回车键触发筛选功能
    bindFilterEnterKey();
    
    // 这里可以添加其他事件绑定
    console.log('✅ 事件绑定完成 - 包含回车键筛选功能');
}

// 刷新数据
function refreshData() {
    currentPage = 1;
    loadData();
}

// 更新记录数显示
function updateRecordCount() {
    const recordCount = document.getElementById('recordCount');
    if (recordCount) {
        recordCount.textContent = `${totalRecords} 条记录`;
    }

    // 更新分页信息显示
    const currentPageDataLength = tableData ? tableData.length : 0;
    const startRecord = totalRecords > 0 ? (currentPage - 1) * pageSize + 1 : 0;
    const endRecord = Math.min(currentPage * pageSize, totalRecords);

    // 更新页面范围显示（如果存在相应元素）
    const pageRangeElement = document.getElementById('pageRange');
    if (pageRangeElement) {
        pageRangeElement.textContent = `显示 ${startRecord}-${endRecord} 条，共 ${totalRecords} 条记录`;
    }

    console.log(`📊 记录统计更新: 总记录数=${totalRecords}, 当前页=${currentPage}/${totalPages}, 当前页数据=${currentPageDataLength}条`);
}

// 渲染分页
function renderPagination() {
    const pagination = document.getElementById('pagination');
    if (!pagination) return;

    pagination.innerHTML = '';

    if (totalPages <= 1) return;

    // 上一页
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${currentPage - 1})">上一页</a>`;
    pagination.appendChild(prevLi);

    // 页码
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    for (let i = startPage; i <= endPage; i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === currentPage ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" onclick="changePage(${i})">${i}</a>`;
        pagination.appendChild(li);
    }

    // 下一页
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${currentPage + 1})">下一页</a>`;
    pagination.appendChild(nextLi);
}

// 切换页面
function changePage(page) {
    if (page < 1 || page > totalPages || page === currentPage) return;
    currentPage = page;
    loadData();
}

// 改变每页显示数量
function changePageSize() {
    const pageSizeSelect = document.getElementById('pageSize');
    pageSize = parseInt(pageSizeSelect.value);
    currentPage = 1;
    loadData();
}

// CRUD功能实现
let currentEditingRecord = null;
let currentEditingIndex = -1;

function addRecord() {
    currentEditingRecord = null;
    currentEditingIndex = -1;

    // 设置模态框标题
    document.getElementById('editModalLabel').textContent = '新增记录';

    // 生成表单字段
    generateFormFields();

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('editModal'));
    modal.show();
}

function editRecord(index) {
    if (index < 0 || index >= tableData.length) {
        showError('无效的记录索引');
        return;
    }

    currentEditingRecord = tableData[index];
    currentEditingIndex = index;

    // 设置模态框标题
    document.getElementById('editModalLabel').textContent = '编辑记录';

    // 生成表单字段并填充数据
    generateFormFields(currentEditingRecord);

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('editModal'));
    modal.show();
}

function deleteRecord(index) {
    if (index < 0 || index >= tableData.length) {
        showError('无效的记录索引');
        return;
    }

    const record = tableData[index];
    currentEditingRecord = record;
    currentEditingIndex = index;

    // 显示记录信息
    const recordInfo = document.getElementById('deleteRecordInfo');
    recordInfo.innerHTML = `
        <div class="alert alert-warning">
            <strong>即将删除的记录:</strong><br>
            ${formatRecordInfo(record)}
        </div>
    `;

    // 显示删除确认模态框
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

// 构建导出列配置
function buildExportColumns() {
    const columns = [];
    
    if (currentColumns && currentColumns.length > 0) {
        currentColumns.forEach(column => {
            if (!column.hidden && column.name !== 'id') {
                columns.push({
                    field: column.name,
                    header: column.display_name || column.name
                });
            }
        });
        console.log(`✅ 构建导出列配置: ${columns.length} 列`);
    } else if (tableData.length > 0) {
        const firstRow = tableData[0];
        Object.keys(firstRow).forEach(key => {
            if (key !== 'id') {
                columns.push({
                    field: key,
                    header: key.toUpperCase()
                });
            }
        });
    }
    
    return columns;
}

// 导出当前页数据
function exportCurrentPage() {
    try {
        showLoading(true);
        console.log(`📄 导出当前页: ${TABLE_NAME}, 第${currentPage}页`);

        if (!tableData || tableData.length === 0) {
            showError('当前页没有数据可导出');
            return;
        }

        if (typeof XLSX === 'undefined') {
            console.warn('⚠️ XLSX.js库未加载，回退到API导出');
            exportDataAPI('current_page');
            return;
        }

        const columns = buildExportColumns();
        const filename = `${TABLE_NAME}_第${currentPage}页`;
        
        unifiedExportData(
            tableData,
            columns,
            filename,
            TABLE_NAME,
            () => showSuccess(`✅ 当前页导出成功！${tableData.length} 条记录`),
            (error) => {
                console.error('❌ 当前页导出失败:', error);
                showError(`当前页导出失败: ${error.message}`);
            }
        );

    } catch (error) {
        console.error('❌ 导出异常:', error);
        showError(`导出异常: ${error.message}`);
    } finally {
        showLoading(false);
    }
}

// 导出筛选结果数据 - 完全按照其他导出功能实现
async function exportFilteredData() {
    try {
        showLoading(true);
        console.log(`🔍 导出筛选结果: ${TABLE_NAME}`);

        if (!advancedFilters || advancedFilters.length === 0) {
            showInfo('未设置筛选条件，将导出全部数据');
            exportAllData();
            return;
        }

        if (typeof XLSX === 'undefined') {
            console.warn('⚠️ XLSX.js库未加载，回退到API导出');
            exportDataAPI('filtered');
            return;
        }

        // 获取完整的筛选数据
        const allData = await fetchAllDataForExport();
        
        if (!allData || allData.length === 0) {
            showError('没有符合筛选条件的数据');
            return;
        }

        const columns = buildExportColumns();
        const filename = `${TABLE_NAME}_筛选结果`;
        
        unifiedExportData(
            allData,
            columns,
            filename,
            TABLE_NAME,
            () => showSuccess(`✅ 筛选结果导出成功！${allData.length} 条记录`),
            (error) => {
                console.error('❌ 筛选结果导出失败:', error);
                showError(`筛选结果导出失败: ${error.message}`);
                // 回退到API导出
                exportDataAPI('filtered');
            }
        );

    } catch (error) {
        console.error('❌ 筛选结果导出异常:', error);
        showError(`筛选结果导出异常: ${error.message}`);
        // 回退到API导出
        exportDataAPI('filtered');
    } finally {
        showLoading(false);
    }
}

// 导出全部数据
async function exportAllData() {
    try {
        showLoading(true);
        console.log(`📊 导出全部数据: ${TABLE_NAME}`);

        if (typeof XLSX === 'undefined') {
            console.warn('⚠️ XLSX.js库未加载，回退到API导出');
            exportDataAPI('all');
            return;
        }

        // 获取全部数据用于前端导出
        const allData = await fetchAllDataForExport();
        
        if (!allData || allData.length === 0) {
            showError('没有数据可导出');
            return;
        }

        const columns = buildExportColumns();
        const filename = `${TABLE_NAME}_全部数据`;
        
        unifiedExportData(
            allData,
            columns,
            filename,
            TABLE_NAME,
            () => showSuccess(`✅ 全部数据导出成功！${allData.length} 条记录`),
            (error) => {
                console.error('❌ 全部数据导出失败:', error);
                showError(`全部数据导出失败: ${error.message}`);
                // 回退到API导出
                exportDataAPI('all');
            }
        );

    } catch (error) {
        console.error('❌ 全部数据导出异常:', error);
        showError(`全部数据导出异常: ${error.message}`);
        // 回退到API导出
        exportDataAPI('all');
    } finally {
        showLoading(false);
    }
}

// 获取全部数据用于导出
async function fetchAllDataForExport() {
    try {
        console.log(`🔍 正在获取全部数据: ${TABLE_NAME}`);
        
        const params = new URLSearchParams({
            page: 1,
            per_page: 100000  // 获取大量数据
        });

        // 添加当前的筛选和排序条件
        if (advancedFilters && advancedFilters.length > 0) {
            params.append('filters', JSON.stringify(advancedFilters));
        }
        
        if (sortColumn) {
            params.append('sort_by', sortColumn);
            params.append('sort_order', sortDirection);
        }

        // 添加时间戳参数防止缓存
        params.append('_t', Date.now());

        const response = await fetch(`${API_BASE}/tables/${TABLE_NAME}/data?${params}`, {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        
        if (result.success) {
            console.log(`✅ 获取全部数据成功: ${result.data.length} 条记录`);
            return result.data;
        } else {
            throw new Error(result.error || '获取全部数据失败');
        }
    } catch (error) {
        console.error('❌ 获取全部数据失败:', error);
        throw error;
    }
}

// 保留原有导出函数作为兼容性接口（默认导出筛选结果）
function exportData() {
    exportFilteredData();
}



// API导出函数 - 支持不同导出类型 (修复版本)
async function exportDataAPI(exportType = 'filtered') {
    try {
        showLoading(true);
        
        let logMessage = '';
        const params = new URLSearchParams({
            format: 'excel',
            export_type: exportType
        });

        switch (exportType) {
            case 'current_page':
                logMessage = `📄 API导出当前页: ${TABLE_NAME}, 第${currentPage}页`;
                params.append('page', currentPage);
                params.append('per_page', pageSize);
                break;
            case 'filtered':
                logMessage = `🔍 API导出筛选结果: ${TABLE_NAME}`;
                if (advancedFilters && advancedFilters.length > 0) {
                    params.append('filters', JSON.stringify(advancedFilters));
                }
                break;
            case 'all':
                logMessage = `📊 API导出全部数据: ${TABLE_NAME}`;
                break;
            default:
                logMessage = `🔄 API导出: ${TABLE_NAME}`;
        }
        
        console.log(logMessage);

        // 添加排序条件
        if (sortColumn) {
            params.append('sort_by', sortColumn);
            params.append('sort_order', sortDirection);
        }

        const exportUrl = `${API_BASE}/tables/${TABLE_NAME}/export?${params}`;

        // 🔧 修复：使用fetch API处理下载，能更好处理错误
        const response = await fetch(exportUrl, {
            method: 'GET',
            headers: {
                'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/json'
            }
        });

        if (!response.ok) {
            // 尝试解析错误消息
            let errorMessage = '下载失败';
            try {
                const errorData = await response.json();
                errorMessage = errorData.error || errorMessage;
            } catch (e) {
                errorMessage = `HTTP ${response.status}: ${response.statusText}`;
            }
            throw new Error(errorMessage);
        }

        // 检查响应类型
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
            // 如果返回JSON，说明是错误响应
            const errorData = await response.json();
            throw new Error(errorData.error || '导出失败');
        }

        // 获取文件内容
        const blob = await response.blob();
        
        // 创建下载链接
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        
        // 从响应头获取文件名或使用默认名称
        const disposition = response.headers.get('content-disposition');
        let filename = `${TABLE_NAME}_导出.xlsx`;
        if (disposition && disposition.includes('filename=')) {
            const matches = disposition.match(/filename="(.+)"/);
            if (matches && matches[1]) {
                filename = matches[1];
            }
        }
        
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        // 清理URL对象
        window.URL.revokeObjectURL(url);

        const exportTypeText = {
            'current_page': '当前页',
            'filtered': '筛选结果',
            'all': '全部数据'
        }[exportType] || '数据';

        showSuccess(`${exportTypeText}导出完成，文件已开始下载`);
        console.log(`✅ ${exportTypeText}导出完成: ${filename}`);
        
    } catch (error) {
        console.error('❌ API导出失败:', error);
        showError('导出失败: ' + error.message);
    } finally {
        showLoading(false);
    }
}



// 生成表单字段
function generateFormFields(record = null) {
    const formFields = document.getElementById('formFields');
    formFields.innerHTML = '';

    if (!currentColumns.length) {
        formFields.innerHTML = '<div class="alert alert-warning">无法获取字段信息</div>';
        return;
    }

    currentColumns.forEach(column => {
        if (column.hidden || column.primary_key) return; // 跳过隐藏字段和主键

        const fieldDiv = document.createElement('div');
        fieldDiv.className = 'mb-3';

        const label = document.createElement('label');
        label.className = 'form-label';
        label.textContent = column.display_name || column.name;
        if (column.nullable === false) {
            label.innerHTML += ' <span class="text-danger">*</span>';
        }

        let input;
        const value = record ? (record[column.name] || '') : '';

        // 根据字段类型创建不同的输入控件
        switch (column.type) {
            case 'datetime':
                input = document.createElement('input');
                input.type = 'datetime-local';
                input.className = 'form-control';
                if (value) {
                    try {
                        const date = new Date(value);
                        input.value = date.toISOString().slice(0, 16);
                    } catch (e) {
                        input.value = '';
                    }
                }
                break;
            case 'status_badge':
                input = document.createElement('select');
                input.className = 'form-select';
                const statusOptions = ['ONLINE', 'OFFLINE', 'MAINTENANCE', 'ERROR'];
                statusOptions.forEach(status => {
                    const option = document.createElement('option');
                    option.value = status;
                    option.textContent = status;
                    if (value === status) option.selected = true;
                    input.appendChild(option);
                });
                break;
            default:
                input = document.createElement('input');
                input.type = 'text';
                input.className = 'form-control';
                input.value = value;
        }

        input.name = column.name;
        input.required = !column.nullable;

        fieldDiv.appendChild(label);
        fieldDiv.appendChild(input);
        formFields.appendChild(fieldDiv);
    });
}

// 保存记录
function saveRecord() {
    try {
        const form = document.getElementById('editForm');
        const formData = new FormData(form);
        const data = {};

        // 收集表单数据
        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }

        // 验证必填字段
        const missingFields = [];
        currentColumns.forEach(column => {
            if (!column.nullable && !column.hidden && !column.primary_key) {
                if (!data[column.name] || data[column.name].trim() === '') {
                    missingFields.push(column.display_name || column.name);
                }
            }
        });

        if (missingFields.length > 0) {
            showError(`请填写必填字段: ${missingFields.join(', ')}`);
            return;
        }

        showLoading(true);

        let url, method;
        if (currentEditingRecord) {
            // 编辑模式 - 使用正确的RESTful URL
            const recordId = currentEditingRecord.id;
            url = `${API_BASE}/tables/${TABLE_NAME}/data/${recordId}`;
            method = 'PUT';
        } else {
            // 新增模式
            url = `${API_BASE}/tables/${TABLE_NAME}/data`;
            method = 'POST';
        }

        fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'same-origin',
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showSuccess(currentEditingRecord ? '记录更新成功' : '记录创建成功');

                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('editModal'));
                modal.hide();

                // 刷新数据
                loadData();
            } else {
                showError(result.error || '保存失败');
            }
        })
        .catch(error => {
            console.error('保存失败:', error);
            showError('保存失败: ' + error.message);
        })
        .finally(() => {
            showLoading(false);
        });

    } catch (error) {
        console.error('保存记录失败:', error);
        showError('保存记录失败: ' + error.message);
        showLoading(false);
    }
}

// 确认删除
function confirmDelete() {
    if (!currentEditingRecord) {
        showError('没有选择要删除的记录');
        return;
    }

    showLoading(true);

    const recordId = currentEditingRecord.id;
    fetch(`${API_BASE}/tables/${TABLE_NAME}/data/${recordId}`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showSuccess('记录删除成功');

            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('deleteModal'));
            modal.hide();

            // 刷新数据
            loadData();
        } else {
            showError(result.error || '删除失败');
        }
    })
    .catch(error => {
        console.error('删除失败:', error);
        showError('删除失败: ' + error.message);
    })
    .finally(() => {
        showLoading(false);
    });
}

// 格式化记录信息用于显示
function formatRecordInfo(record) {
    const displayFields = currentColumns.filter(col => !col.hidden).slice(0, 3);
    return displayFields.map(col => {
        const value = record[col.name] || '';
        return `<strong>${col.display_name || col.name}:</strong> ${value}`;
    }).join('<br>');
}

// 筛选功能实现
function addFilterCondition() {
    filterConditionIndex++;
    const filterConditions = document.getElementById('filterConditions');

    const newFilterRow = document.createElement('div');
    newFilterRow.className = 'filter-row';
    newFilterRow.setAttribute('data-index', filterConditionIndex);

    newFilterRow.innerHTML = `
        <div class="filter-field">
            <label class="form-label form-label-sm">字段</label>
            <select class="form-select form-select-sm" name="field">
                <option value="">请选择字段</option>
                ${currentColumns.filter(col => !col.hidden).map(col =>
                    `<option value="${col.name}">${col.display_name || col.name}</option>`
                ).join('')}
            </select>
        </div>
        <div class="filter-operator">
            <label class="form-label form-label-sm">操作符</label>
            <select class="form-select form-select-sm" name="operator">
                <option value="contains">包含</option>
                <option value="equals">等于</option>
                <option value="starts_with">开始于</option>
                <option value="ends_with">结束于</option>
                <option value="not_equals">不等于</option>
            </select>
        </div>
        <div class="filter-value">
            <label class="form-label form-label-sm">值</label>
            <input type="text" class="form-control form-control-sm" name="value" placeholder="输入筛选值">
        </div>
        <div class="filter-actions">
            <label class="form-label form-label-sm">&nbsp;</label>
            <div>
                <button type="button" class="btn btn-sm btn-success" onclick="addFilterCondition()" title="添加条件">
                    <i class="fas fa-plus"></i>
                </button>
                <button type="button" class="btn btn-sm btn-danger" onclick="removeFilterCondition(${filterConditionIndex})" title="删除条件">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
        </div>
    `;

    filterConditions.appendChild(newFilterRow);
}

function removeFilterCondition(index) {
    const filterRow = document.querySelector(`[data-index="${index}"]`);
    if (filterRow) {
        filterRow.remove();
    }
}

function applyFilter() {
    const filterRows = document.querySelectorAll('.filter-row');
    advancedFilters = [];

    filterRows.forEach(row => {
        const field = row.querySelector('select[name="field"]').value;
        const operator = row.querySelector('select[name="operator"]').value;
        const value = row.querySelector('input[name="value"]').value;

        if (field && value) {
            advancedFilters.push({
                field: field,
                operator: operator,
                value: value
            });
        }
    });

    console.log('应用筛选条件:', advancedFilters);
    currentPage = 1; // 重置到第一页
    loadData();
}

// 绑定回车键事件到筛选输入框
function bindFilterEnterKey() {
    // 为所有筛选输入框绑定回车键事件
    document.addEventListener('keydown', function(e) {
        // 检查是否在筛选输入框中按下回车键
        if (e.key === 'Enter' && e.target.matches('.filter-row input[name="value"]')) {
            e.preventDefault();
            console.log('🔍 回车键触发筛选');
            applyFilter();
        }
    });
    
    // 为动态添加的筛选输入框绑定事件
    document.addEventListener('input', function(e) {
        if (e.target.matches('.filter-row input[name="value"]')) {
            // 为新添加的输入框绑定回车键事件
            e.target.addEventListener('keydown', function(event) {
                if (event.key === 'Enter') {
                    event.preventDefault();
                    console.log('🔍 回车键触发筛选 (动态绑定)');
                    applyFilter();
                }
            });
        }
    });
}

function clearFilter() {
    advancedFilters = [];

    // 清空筛选表单
    const filterRows = document.querySelectorAll('.filter-row');
    filterRows.forEach((row, index) => {
        if (index > 0) { // 保留第一行
            row.remove();
        } else {
            // 重置第一行
            row.querySelector('select[name="field"]').value = '';
            row.querySelector('select[name="operator"]').value = 'contains';
            row.querySelector('input[name="value"]').value = '';
        }
    });

    currentPage = 1;
    loadData();
}

function sortTable(column) {
    if (sortColumn === column) {
        sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
        sortColumn = column;
        sortDirection = 'asc';
    }

    // 更新表头排序图标
    updateSortIcons(column, sortDirection);

    loadData();
}

// 更新排序图标
function updateSortIcons(activeColumn, direction) {
    // 重置所有排序图标
    document.querySelectorAll('.sort-icons i').forEach(icon => {
        icon.className = 'fas fa-sort text-muted';
    });

    // 设置当前排序列的图标
    const activeIcon = document.querySelector(`[onclick="sortTable('${activeColumn}')"] i`);
    if (activeIcon) {
        activeIcon.className = direction === 'asc' ? 'fas fa-sort-up text-primary' : 'fas fa-sort-down text-primary';
    }
}

function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const rowSelects = document.querySelectorAll('.row-select');

    rowSelects.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });

    updateBatchOperations();
}

function updateBatchOperations() {
    const selectedRows = document.querySelectorAll('.row-select:checked');
    const batchOperations = document.getElementById('batchOperations');
    const selectedCount = document.getElementById('selectedCount');

    if (selectedRows.length > 0) {
        batchOperations.style.display = 'block';
        selectedCount.textContent = selectedRows.length;
    } else {
        batchOperations.style.display = 'none';
    }
}

function batchDelete() {
    const selectedRows = document.querySelectorAll('.row-select:checked');
    if (selectedRows.length === 0) {
        showError('请先选择要删除的记录');
        return;
    }

    // 更新批量删除模态框中的数量
    document.getElementById('batchDeleteCount').textContent = selectedRows.length;

    // 显示批量删除确认模态框
    const modal = new bootstrap.Modal(document.getElementById('batchDeleteModal'));
    modal.show();
}

function confirmBatchDelete() {
    const selectedRows = document.querySelectorAll('.row-select:checked');
    if (selectedRows.length === 0) {
        showError('没有选择要删除的记录');
        return;
    }

    // 收集要删除的记录ID
    const idsToDelete = [];
    selectedRows.forEach(checkbox => {
        const rowIndex = parseInt(checkbox.value);
        if (rowIndex >= 0 && rowIndex < tableData.length) {
            const record = tableData[rowIndex];
            if (record.id) {
                idsToDelete.push(record.id);
            }
        }
    });

    if (idsToDelete.length === 0) {
        showError('无法获取要删除的记录ID');
        return;
    }

    showLoading(true);

    // 发送批量删除请求
    fetch(`${API_BASE}/tables/${TABLE_NAME}/data/batch`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'same-origin',
        body: JSON.stringify({ ids: idsToDelete })
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showSuccess(`成功删除 ${idsToDelete.length} 条记录`);

            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('batchDeleteModal'));
            modal.hide();

            // 清除选择
            clearSelection();

            // 刷新数据
            loadData();
        } else {
            showError(result.error || '批量删除失败');
        }
    })
    .catch(error => {
        console.error('批量删除失败:', error);
        showError('批量删除失败: ' + error.message);
    })
    .finally(() => {
        showLoading(false);
    });
}

function clearSelection() {
    document.querySelectorAll('.row-select').forEach(checkbox => {
        checkbox.checked = false;
    });
    document.getElementById('selectAll').checked = false;
    updateBatchOperations();
}

// Excel导入相关函数
function checkExcelImportSupport() {
    // 检查当前表是否支持Excel导入
    const supportedTables = ['devicepriorityconfig', 'lotpriorityconfig'];
    const excelImportBtn = document.getElementById('excelImportBtn');

    if (supportedTables.includes(TABLE_NAME)) {
        excelImportBtn.style.display = 'inline-block';
        console.log(`表 ${TABLE_NAME} 支持Excel导入功能`);
    } else {
        excelImportBtn.style.display = 'none';
        console.log(`表 ${TABLE_NAME} 不支持Excel导入功能`);
    }
}

function showExcelUploadModal() {
    const modal = new bootstrap.Modal(document.getElementById('excelUploadModal'));

    // 根据表类型设置格式说明
    const formatDetails = document.getElementById('formatDetails');
    const uploadHelpText = document.getElementById('uploadHelpText');

    if (TABLE_NAME === 'devicepriorityconfig') {
        formatDetails.innerHTML = `
            <p><strong>产品优先级配置表 (devicepriorityconfig) 字段：</strong></p>
            <ul>
                <li><strong>DEVICE</strong> (必填): 产品名称</li>
                <li><strong>PRIORITY</strong> (必填): 优先级数值</li>
                <li><strong>FROM_TIME</strong> (可选): 生效开始时间</li>
                <li><strong>END_TIME</strong> (可选): 生效结束时间</li>
                <li><strong>REFRESH_TIME</strong> (可选): 刷新时间</li>
                <li><strong>USER</strong> (可选): 操作用户</li>
            </ul>
            <p class="text-info"><small><i class="fas fa-info-circle me-1"></i>Excel列名必须与上述字段名完全一致（大写）</small></p>
            <p class="text-warning"><small><i class="fas fa-exclamation-triangle me-1"></i>文件将导入到 aps_system.devicepriorityconfig 表</small></p>
        `;
        uploadHelpText.textContent = '仅支持单个Excel文件上传，文件名必须包含"device"关键字，Excel列名必须为大写。';
    } else if (TABLE_NAME === 'lotpriorityconfig') {
        formatDetails.innerHTML = `
            <p><strong>批次优先级配置表 (lotpriorityconfig) 字段：</strong></p>
            <ul>
                <li><strong>DEVICE</strong> (必填): 产品名称</li>
                <li><strong>PRIORITY</strong> (必填): 优先级数值</li>
                <li><strong>STAGE</strong> (可选): 工艺阶段</li>
                <li><strong>REFRESH_TIME</strong> (可选): 刷新时间</li>
                <li><strong>USER</strong> (可选): 操作用户</li>
            </ul>
            <p class="text-info"><small><i class="fas fa-info-circle me-1"></i>Excel列名必须与上述字段名完全一致（大写）</small></p>
            <p class="text-warning"><small><i class="fas fa-exclamation-triangle me-1"></i>文件将导入到 aps_system.lotpriorityconfig 表</small></p>
        `;
        uploadHelpText.textContent = '仅支持单个Excel文件上传，文件名必须包含"lot"关键字，Excel列名必须为大写。';
    }

    modal.show();

    // 重置表单
    document.getElementById('excelFiles').value = '';
    document.getElementById('uploadProgress').style.display = 'none';
    document.getElementById('uploadResult').style.display = 'none';
}

function uploadExcelFiles() {
    const fileInput = document.getElementById('excelFiles');
    const files = fileInput.files;

    if (files.length === 0) {
        alert('请选择要上传的Excel文件');
        return;
    }

    if (files.length > 1) {
        alert('仅支持单个文件上传，请选择一个Excel文件');
        return;
    }

    const file = files[0];
    const fileName = file.name.toLowerCase();

    // 严格验证文件名和表类型匹配
    if (TABLE_NAME === 'devicepriorityconfig') {
        if (!fileName.includes('device')) {
            alert('产品优先级配置文件名必须包含"device"关键字');
            return;
        }
    } else if (TABLE_NAME === 'lotpriorityconfig') {
        if (!fileName.includes('lot')) {
            alert('批次优先级配置文件名必须包含"lot"关键字');
            return;
        }
    } else {
        alert('当前表不支持Excel导入功能');
        return;
    }

    // 验证文件格式
    if (!fileName.endsWith('.xlsx') && !fileName.endsWith('.xls')) {
        alert('仅支持Excel文件格式(.xlsx或.xls)');
        return;
    }

    const progressBar = document.querySelector('.progress-bar');
    const progressContainer = document.getElementById('uploadProgress');
    const statusText = document.getElementById('uploadStatus');
    const resultContainer = document.getElementById('uploadResult');

    // 显示进度条
    progressContainer.style.display = 'block';
    resultContainer.style.display = 'none';
    progressBar.style.width = '0%';
    statusText.textContent = '准备上传...';

    // 禁用上传按钮
    const uploadBtn = document.querySelector('#excelUploadModal .btn-primary');
    uploadBtn.disabled = true;
    uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>上传中...';

    // 创建FormData - 单文件上传
    const formData = new FormData();
    formData.append('files', file);

    // 上传文件
    fetch('/api/v2/production/priority-settings/upload', {
        method: 'POST',
        body: formData,
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        progressBar.style.width = '100%';
        statusText.textContent = '上传完成';

        resultContainer.style.display = 'block';

        if (data.success) {
            resultContainer.className = 'alert alert-success';
            const result = data.results[0]; // 单文件结果
            resultContainer.innerHTML = `
                <h6><i class="fas fa-check-circle me-1"></i>上传成功</h6>
                <p><strong>文件:</strong> ${result.filename}</p>
                <p><strong>目标表:</strong> aps_system.${TABLE_NAME}</p>
                <p><strong>导入记录:</strong> ${result.imported_count} 条</p>
                ${result.errors && result.errors.length > 0 ?
                    `<div class="mt-2">
                        <strong>警告信息:</strong>
                        <ul class="mb-0">
                            ${result.errors.map(error => `<li class="text-warning">${error}</li>`).join('')}
                        </ul>
                    </div>` : ''
                }
            `;

            // 刷新数据
            setTimeout(() => {
                loadData();
                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('excelUploadModal'));
                modal.hide();
            }, 2000);
        } else {
            resultContainer.className = 'alert alert-danger';
            resultContainer.innerHTML = `
                <h6><i class="fas fa-exclamation-triangle me-1"></i>上传失败</h6>
                <p>${data.message || '未知错误'}</p>
            `;
        }
    })
    .catch(error => {
        progressBar.style.width = '100%';
        statusText.textContent = '上传失败';

        resultContainer.style.display = 'block';
        resultContainer.className = 'alert alert-danger';
        resultContainer.innerHTML = `
            <h6><i class="fas fa-exclamation-triangle me-1"></i>上传失败</h6>
            <p>${error.message}</p>
        `;
    })
    .finally(() => {
        // 恢复上传按钮
        uploadBtn.disabled = false;
        uploadBtn.innerHTML = '开始上传';
    });
}

 // 调整模式全局变量
 let adjustmentData = [];
 let originalData = [];
 let adjustmentHistory = [];
 let groupedData = {};

 // 调整模式功能已迁移至 /production/done-lots 页面
// 此页面仅保留普通的数据查看功能
function setupModeToggle() {
         // 调整功能已迁移至 /production/done-lots 页面
     // 如果有调整参数，直接跳转到正确的页面
     const urlParams = new URLSearchParams(window.location.search);
     const mode = urlParams.get('mode');
     
     if (mode === 'adjust' && TABLE_NAME === 'lotprioritydone') {
         window.location.href = `/production/done-lots?mode=adjust&session=${Date.now()}`;
     }
 }

 // 调整模式功能已迁移至 /production/done-lots 页面

</script>
{% endblock %}
