#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
已排产批次API接口
提供lotprioritydone表的数据查询、操作接口

🚀 缓存统一化修复版本：
- 使用APIDataCacheAdapter替代直接数据库查询
- 提升性能，降低数据库负载
- 保持API接口兼容性
"""

import logging
import os
import re
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify
from sqlalchemy import text
from app import db
from app.utils.api_config import get_api_route
from flask_login import login_required
# 🚀 新增：统一缓存适配器
from app.utils.api_cache_adapter import get_api_cache_adapter, get_cached_table_data
# 连接池接口（保留作为降级方案）
from app.utils.db_helper import get_mysql_connection

logger = logging.getLogger(__name__)

def _get_mapped_stage(stage):
    """浏览接口禁用工序映射，直接返回原始STAGE"""
    # ⚠️ 仅在排产服务内部启用映射，浏览数据接口不做任何转换
    return stage

def _get_historical_equipment_recommendation(device, stage):
    """
    🔥 高优先级任务2：基于历史生产记录的设备推荐（缓存优化版）
    按照最新生产时间（CREATE_TIME）降序排列历史记录，而不是按生产次数
    只参考最近6个月内的历史生产记录，过期记录不作为参考
    使用STAGE映射表进行工序名称转换
    
    🚀 缓存优化：使用APIDataCacheAdapter替代直接数据库查询
    """
    if not device or not stage:
        return None

    try:
        # 获取映射后的工序名称
        mapped_stage = _get_mapped_stage(stage)
        
        # 🚀 使用缓存适配器获取历史设备数据
        api_cache = get_api_cache_adapter()
        historical_data = api_cache.get_historical_equipment_data(device, mapped_stage, days_limit=180)
        
        if not historical_data:
            return None
        
        # 处理历史数据，按最新生产时间排序
        equipment_stats = {}
        for record in historical_data:
            handler_id = record.get('AUXILIARY_EQP_ID')
            if not handler_id or handler_id == '':
                continue
                
            create_time = record.get('CREATE_TIME')
            first_pass_yield = record.get('FIRST_PASS_YIELD')
            final_yield = record.get('FINAL_YIELD')
            
            # 跳过无效数据
            if not first_pass_yield or first_pass_yield == '':
                continue
                
            if handler_id not in equipment_stats:
                equipment_stats[handler_id] = {
                    'production_count': 0,
                    'last_production_time': None,
                    'first_production_time': None,
                    'yields': []
                }
            
            stats = equipment_stats[handler_id]
            stats['production_count'] += 1
            stats['yields'].append({
                'first_pass': first_pass_yield,
                'final': final_yield
            })
            
            # 更新时间统计
            if isinstance(create_time, datetime):
                if stats['last_production_time'] is None or create_time > stats['last_production_time']:
                    stats['last_production_time'] = create_time
                if stats['first_production_time'] is None or create_time < stats['first_production_time']:
                    stats['first_production_time'] = create_time
        
        if not equipment_stats:
            return None
        
        # 按最新生产时间排序，获取推荐设备
        sorted_equipment = sorted(
            equipment_stats.items(),
            key=lambda x: (x[1]['last_production_time'] or datetime.min, x[1]['production_count']),
            reverse=True
        )
        
        # 获取最佳推荐
        handler_id, stats = sorted_equipment[0]
        production_count = stats['production_count']
        last_time = stats['last_production_time']
        
        # 计算平均良率
        yields = stats['yields']
        avg_first_pass_yield = None
        avg_final_yield = None
        
        if yields:
            valid_first_yields = [float(y['first_pass']) for y in yields if y['first_pass'] and y['first_pass'] != '']
            valid_final_yields = [float(y['final']) for y in yields if y['final'] and y['final'] != '']
            
            if valid_first_yields:
                avg_first_pass_yield = sum(valid_first_yields) / len(valid_first_yields)
            if valid_final_yields:
                avg_final_yield = sum(valid_final_yields) / len(valid_final_yields)
        
        # 🚀 使用缓存获取设备状态
        equipment_result = get_cached_table_data('eqp_status', filters=[
            {'field': 'HANDLER_ID', 'operator': 'equals', 'value': handler_id}
        ])
        
        status = None
        if equipment_result.get('success') and equipment_result.get('data'):
            equipment_info = equipment_result['data'][0]
            status = equipment_info.get('STATUS')
        
        # 格式化时间显示
        if isinstance(last_time, datetime):
            time_str = last_time.strftime('%Y-%m-%d')
        else:
            time_str = str(last_time)[:10] if last_time else '未知'

        # 格式化良率显示（优先显示最终良率）
        if avg_final_yield is not None:
            yield_str = f"最终良率: {avg_final_yield*100:.1f}%"
        elif avg_first_pass_yield is not None:
            yield_str = f"首通良率: {avg_first_pass_yield*100:.1f}%"
        else:
            yield_str = "良率: 未知"

        # 判断设备可用性
        if status:
            available_statuses = ['IDLE', 'Run', 'Wait', '0', 'READY', 'ONLINE', 'SetupRun', '']
            is_available = status in available_statuses
            availability_text = "可用" if is_available else f"当前状态: {status}"
        else:
            availability_text = "设备状态未知"

        return f"💡 历史推荐: {handler_id} (最后生产: {time_str}, 历史成功: {production_count}次, {yield_str}, {availability_text})"

    except Exception as e:
        logger.error(f"获取历史设备推荐失败: {e}")
        # 🚀 降级机制：如果缓存失败，使用原有逻辑
        try:
            return _get_historical_equipment_recommendation_legacy(device, stage)
        except Exception as fallback_error:
            logger.error(f"降级方案也失败: {fallback_error}")
            return None

def _get_historical_equipment_recommendation_legacy(device, stage):
    """
    历史设备推荐的降级方案（原有直接查询逻辑）
    仅在缓存方案失败时使用
    """
    if not device or not stage:
        return None

    try:
        mapped_stage = _get_mapped_stage(stage)
        six_months_ago = datetime.now() - timedelta(days=180)

        query = text("""
            SELECT AUXILIARY_EQP_ID as HANDLER_ID, COUNT(*) as production_count,
                   MAX(CREATE_TIME) as last_production_time,
                   MIN(CREATE_TIME) as first_production_time,
                   AVG(CAST(FIRST_PASS_YIELD as DECIMAL(5,4))) as avg_first_pass_yield,
                   AVG(CAST(FINAL_YIELD as DECIMAL(5,4))) as avg_final_yield
            FROM ct
            WHERE DEVICE = :device AND STAGE = :stage
            AND CREATE_TIME >= :six_months_ago
            AND AUXILIARY_EQP_ID IS NOT NULL
            AND AUXILIARY_EQP_ID != ''
            AND FIRST_PASS_YIELD IS NOT NULL
            AND FIRST_PASS_YIELD != ''
            GROUP BY AUXILIARY_EQP_ID
            ORDER BY MAX(CREATE_TIME) DESC, COUNT(*) DESC
            LIMIT 3
        """)

        result = db.session.execute(query, {
            'device': device,
            'stage': mapped_stage,
            'six_months_ago': six_months_ago
        })

        historical_records = result.fetchall()

        if historical_records:
            top_record = historical_records[0]
            handler_id = top_record[0]
            production_count = top_record[1]
            last_time = top_record[2]
            avg_first_pass_yield = top_record[4]
            avg_final_yield = top_record[5]

            status_query = text("""
                SELECT STATUS, HANDLER_CONFIG, EQP_CLASS
                FROM eqp_status
                WHERE HANDLER_ID = :handler_id
            """)

            status_result = db.session.execute(status_query, {'handler_id': handler_id})
            status_record = status_result.fetchone()

            if status_record:
                status = status_record[0]
                available_statuses = ['IDLE', 'Run', 'Wait', '0', 'READY', 'ONLINE', 'SetupRun', '']
                is_available = status in available_statuses

                if isinstance(last_time, datetime):
                    time_str = last_time.strftime('%Y-%m-%d')
                else:
                    time_str = str(last_time)[:10] if last_time else '未知'

                if avg_final_yield is not None:
                    yield_str = f"最终良率: {avg_final_yield*100:.1f}%"
                elif avg_first_pass_yield is not None:
                    yield_str = f"首通良率: {avg_first_pass_yield*100:.1f}%"
                else:
                    yield_str = "良率: 未知"

                availability_text = "可用" if is_available else f"当前状态: {status}"
                return f"💡 历史推荐: {handler_id} (最后生产: {time_str}, 历史成功: {production_count}次, {yield_str}, {availability_text})"

        return None

    except Exception as e:
        logger.error(f"降级方案获取历史设备推荐失败: {e}")
        return None

def generate_suggestion(failure_reason, failure_details, device=None, stage=None):
    """
    生成失败原因的建议解决方案
    🔥 高优先级任务2：优化历史生产记录参考逻辑
    """
    base_suggestion = ""

    if "配置需求获取失败" in failure_reason:
        base_suggestion = "请在ET_FT_TEST_SPEC表中补充对应器件和工序的测试规范"
    elif "无合适设备" in failure_reason:
        base_suggestion = "请检查设备状态和配置匹配，确保有可用的设备"
    elif "设备ID无效" in failure_reason:
        base_suggestion = "请检查设备配置，确保HANDLER_ID字段正确"
    elif "算法执行异常" in failure_reason:
        base_suggestion = "请检查系统日志，可能需要技术支持"
    elif "测试规范缺失" in failure_reason:
        base_suggestion = "请在ET_FT_TEST_SPEC表中补充对应器件和工序的测试规范"
    elif "配置" in failure_reason or "config" in failure_reason.lower():
        base_suggestion = "请检查配置设置和参数是否正确"
    elif "设备" in failure_reason or "不兼容" in failure_reason:
        base_suggestion = "请检查设备兼容性和状态"
    else:
        base_suggestion = "请联系技术支持进行详细分析"

    # 浏览接口性能优化：不再实时调用历史生产记录
    return base_suggestion

# 创建蓝图 - 使用配置化的URL前缀
done_lots_bp = Blueprint('done_lots_api', __name__)

# 兼容性导出
done_lots_api = done_lots_bp

@done_lots_bp.route(get_api_route('production/done-lots'), methods=['GET'])
def get_lotprioritydone_data():
    """
    获取已排产表数据
    支持分页、筛选、排序
    """
    try:
        # 获取请求参数
        table = request.args.get('table', 'lotprioritydone')
        page = int(request.args.get('page', 1))
        size = int(request.args.get('size', 50))
        search = request.args.get('search', '').strip()  # 全局搜索
        sort_by = request.args.get('sort_by', 'PRIORITY')
        sort_order = request.args.get('sort_order', 'ASC')
        
        # 只处理lotprioritydone表的请求
        if table != 'lotprioritydone':
            return jsonify({
                'success': False,
                'message': f'不支持的表名: {table}'
            }), 400
        
        # 构建基础查询条件
        where_conditions = []
        query_params = {}
        
        # 处理全局搜索
        if search:
            search_conditions = [
                "LOT_ID LIKE :search",
                "DEVICE LIKE :search", 
                "CHIP_ID LIKE :search",
                "HANDLER_ID LIKE :search",
                "PO_ID LIKE :search",
                "STAGE LIKE :search",
                "STEP LIKE :search",
                "match_type LIKE :search"
            ]
            where_conditions.append(f"({' OR '.join(search_conditions)})")
            query_params['search'] = f'%{search}%'
        
        # 构建WHERE子句
        where_clause = ""
        if where_conditions:
            where_clause = "WHERE " + " AND ".join(where_conditions)
        
        # 构建ORDER BY子句
        valid_sort_fields = ['PRIORITY', 'CREATE_TIME', 'LOT_ID', 'DEVICE', 'HANDLER_ID', 'GOOD_QTY', 'STAGE', 'STEP']
        if sort_by not in valid_sort_fields:
            sort_by = 'PRIORITY'
        
        if sort_order.upper() not in ['ASC', 'DESC']:
            sort_order = 'ASC'
            
        # 如果按PRIORITY排序，添加CREATE_TIME作为次要排序
        if sort_by == 'PRIORITY':
            order_clause = f"ORDER BY {sort_by} {sort_order}, CREATE_TIME DESC"
        else:
            order_clause = f"ORDER BY {sort_by} {sort_order}"
        
        # 计算偏移量
        offset = (page - 1) * size
        query_params.update({'size': size, 'offset': offset})
        
        # 查询总数（应用筛选条件）
        count_query = text(f"SELECT COUNT(*) FROM lotprioritydone {where_clause}")
        total_result = db.session.execute(count_query, query_params)
        total = total_result.scalar() or 0
        
        # 查询数据（应用筛选和排序）
        data_query = text(f"""
            SELECT 
                id, PRIORITY, HANDLER_ID, LOT_ID, LOT_TYPE, GOOD_QTY,
                PROD_ID, DEVICE, CHIP_ID, PKG_PN, PO_ID, STAGE, STEP,
                WIP_STATE, PROC_STATE, HOLD_STATE, FLOW_ID, FLOW_VER,
                RELEASE_TIME, FAC_ID, CREATE_TIME,
                comprehensive_score, processing_time, changeover_time,
                algorithm_version, match_type, priority_score
            FROM lotprioritydone 
            {where_clause}
            {order_clause}
            LIMIT :size OFFSET :offset
        """)
        
        result = db.session.execute(data_query, query_params)
        
        # 构建返回数据
        records = []
        for row in result.fetchall():
            records.append({
                'id': row[0],  # 使用id作为ID
                'PRIORITY': row[1] or '',
                'HANDLER_ID': row[2] or '',
                'LOT_ID': row[3] or '',
                'LOT_TYPE': row[4] or '',
                'GOOD_QTY': row[5] or 0,
                'PROD_ID': row[6] or '',
                'DEVICE': row[7] or '',
                'CHIP_ID': row[8] or '',
                'PKG_PN': row[9] or '',
                'PO_ID': row[10] or '',
                'STAGE': row[11] or '',
                'STEP': row[12] or '',          # 工步字段
                'WIP_STATE': row[13] or '',
                'PROC_STATE': row[14] or '',
                'HOLD_STATE': row[15] or 0,
                'FLOW_ID': row[16] or '',
                'FLOW_VER': row[17] or '',
                'RELEASE_TIME': row[18] or '',
                'FAC_ID': row[19] or '',
                'CREATE_TIME': row[20] or '',
                'comprehensive_score': row[21] or 0.0,  # 综合评分
                'processing_time': row[22] or 0.0,      # 预计加工时间
                'changeover_time': row[23] or 0.0,      # 改机时间
                'algorithm_version': row[24] or '',     # 算法版本
                'match_type': row[25] or '',            # 匹配类型
                'priority_score': row[26] or 0.0        # 优先级评分
            })
        
        # 计算分页信息
        total_pages = (total + size - 1) // size
        
        logger.info(f"📊 已排产表查询: 第{page}页, {size}条/页, 共{total}条记录, 搜索:'{search}', 排序:{sort_by} {sort_order}")
        
        return jsonify({
            'success': True,
            'data': records,
            'pagination': {
                'page': page,
                'size': size,
                'total': total,
                'total_pages': total_pages
            },
            'filters': {
                'search': search,
                'sort_by': sort_by,
                'sort_order': sort_order
            }
        })
        
    except Exception as e:
        logger.error(f"❌ 获取已排产数据失败: {e}")
        return jsonify({
            'success': False,
            'message': f'查询失败: {str(e)}'
        }), 500

@done_lots_bp.route(get_api_route('production/done-lots/columns'), methods=['GET'])
def get_lotprioritydone_columns():
    """获取已排产表的列信息"""
    try:
        table = request.args.get('table', 'lotprioritydone')
        
        if table != 'lotprioritydone':
            return jsonify({
                'success': False,
                'message': f'不支持的表名: {table}'
            }), 400
        
        # 定义已排产表的列信息（包含算法扩展字段）
        columns = [
            {'name': 'PRIORITY', 'label': '执行优先级', 'type': 'number', 'sortable': True},
            {'name': 'comprehensive_score', 'label': '综合评分', 'type': 'number', 'sortable': True},
            {'name': 'HANDLER_ID', 'label': '分选机编号', 'type': 'string', 'sortable': True},
            {'name': 'LOT_ID', 'label': '内部工单号', 'type': 'string', 'sortable': True},
            {'name': 'LOT_TYPE', 'label': '批次类型', 'type': 'string', 'sortable': True},
            {'name': 'GOOD_QTY', 'label': '良品数量', 'type': 'number', 'sortable': True},
            {'name': 'PROD_ID', 'label': '产品ID', 'type': 'string', 'sortable': True},
            {'name': 'DEVICE', 'label': '产品名称', 'type': 'string', 'sortable': True},
            {'name': 'CHIP_ID', 'label': '芯片名称', 'type': 'string', 'sortable': True},
            {'name': 'PKG_PN', 'label': '封装', 'type': 'string', 'sortable': True},
            {'name': 'PO_ID', 'label': '订单号', 'type': 'string', 'sortable': True},
            {'name': 'STAGE', 'label': '工序', 'type': 'string', 'sortable': True},
            {'name': 'STEP', 'label': '工步', 'type': 'string', 'sortable': True},
            {'name': 'processing_time', 'label': '预计加工时间(h)', 'type': 'number', 'sortable': True},
            {'name': 'changeover_time', 'label': '改机时间(min)', 'type': 'number', 'sortable': True},
            {'name': 'WIP_STATE', 'label': 'WIP状态', 'type': 'string', 'sortable': True},
            {'name': 'PROC_STATE', 'label': '流程状态', 'type': 'string', 'sortable': True},
            {'name': 'HOLD_STATE', 'label': '扣留状态', 'type': 'number', 'sortable': True},
            {'name': 'FLOW_ID', 'label': '流程ID', 'type': 'string', 'sortable': True},
            {'name': 'FLOW_VER', 'label': '流程版本', 'type': 'string', 'sortable': True},
            {'name': 'RELEASE_TIME', 'label': '释放时间', 'type': 'datetime', 'sortable': True},
            {'name': 'FAC_ID', 'label': '工厂ID', 'type': 'string', 'sortable': True},
            {'name': 'CREATE_TIME', 'label': '创建时间', 'type': 'datetime', 'sortable': True},
            {'name': 'priority_score', 'label': '优先级评分', 'type': 'number', 'sortable': True},
            {'name': 'algorithm_version', 'label': '算法版本', 'type': 'string', 'sortable': True},
            {'name': 'match_type', 'label': '匹配类型', 'type': 'string', 'sortable': True}
        ]
        
        return jsonify({
            'success': True,
            'columns': columns
        })
        
    except Exception as e:
        logger.error(f"❌ 获取列信息失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取列信息失败: {str(e)}'
        }), 500

@done_lots_bp.route(get_api_route('production/done-lots/move-to-waiting'), methods=['POST'])
def move_lots_to_waiting():
    """将已排产批次移回待排产状态"""
    try:
        data = request.get_json()
        lot_ids = data.get('ids', [])
        
        if not lot_ids:
            return jsonify({
                'success': False,
                'message': '请选择要移动的批次'
            }), 400
        
        # 获取要移动的批次信息
        placeholders = ','.join([':id_%d' % i for i in range(len(lot_ids))])
        params = {'id_%d' % i: lot_id for i, lot_id in enumerate(lot_ids)}
        
        select_query = text(f"""
            SELECT LOT_ID, DEVICE, STAGE, GOOD_QTY, PKG_PN, CHIP_ID,
                   FLOW_ID, FLOW_VER, FAC_ID, RELEASE_TIME
            FROM lotprioritydone 
            WHERE PRIORITY IN ({placeholders})
        """)
        
        result = db.session.execute(select_query, params)
        lots_to_move = result.fetchall()
        
        if not lots_to_move:
            return jsonify({
                'success': False,
                'message': '未找到指定的批次'
            }), 404
        
        # 将批次信息重新插入到ET_WAIT_LOT表
        moved_count = 0
        for lot in lots_to_move:
            try:
                insert_query = text("""
                    INSERT INTO ET_WAIT_LOT (
                        LOT_ID, DEVICE, STAGE, GOOD_QTY, PKG_PN, CHIP_ID,
                        WIP_STATE, PROC_STATE, HOLD_STATE, FLOW_ID, FLOW_VER,
                        FAC_ID, RELEASE_TIME, CREATE_TIME
                    ) VALUES (
                        :lot_id, :device, :stage, :good_qty, :pkg_pn, :chip_id,
                        'WAIT', 'UNASSIGNED', 0, :flow_id, :flow_ver,
                        :fac_id, :release_time, NOW()
                    )
                """)
                
                db.session.execute(insert_query, {
                    'lot_id': lot[0],
                    'device': lot[1],
                    'stage': lot[2],
                    'good_qty': lot[3],
                    'pkg_pn': lot[4],
                    'chip_id': lot[5],
                    'flow_id': lot[6],
                    'flow_ver': lot[7],
                    'fac_id': lot[8],
                    'release_time': lot[9]
                })
                moved_count += 1
                
            except Exception as e:
                logger.warning(f"移动批次 {lot[0]} 失败: {e}")
                continue
        
        # 从已排产表中删除这些批次
        if moved_count > 0:
            delete_query = text(f"""
                DELETE FROM lotprioritydone 
                WHERE PRIORITY IN ({placeholders})
            """)
            db.session.execute(delete_query, params)
        
        db.session.commit()
        
        logger.info(f"✅ 成功移动 {moved_count} 个批次到待排产状态")
        
        return jsonify({
            'success': True,
            'message': f'成功移动 {moved_count} 个批次到待排产状态',
            'moved_count': moved_count
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"❌ 移动批次失败: {e}")
        return jsonify({
            'success': False,
            'message': f'移动失败: {str(e)}'
        }), 500

@done_lots_bp.route(get_api_route('production/done-lots/delete'), methods=['POST'])
def delete_lotprioritydone_records():
    """删除已排产记录"""
    try:
        data = request.get_json()
        table = data.get('table', 'lotprioritydone')
        ids = data.get('ids', [])
        
        if table != 'lotprioritydone':
            return jsonify({
                'success': False,
                'message': f'不支持的表名: {table}'
            }), 400
        
        if not ids:
            return jsonify({
                'success': False,
                'message': '请选择要删除的记录'
            }), 400
        
        # 构建删除语句
        placeholders = ','.join([':id_%d' % i for i in range(len(ids))])
        params = {'id_%d' % i: record_id for i, record_id in enumerate(ids)}
        
        delete_query = text(f"""
            DELETE FROM lotprioritydone 
            WHERE PRIORITY IN ({placeholders})
        """)
        
        result = db.session.execute(delete_query, params)
        deleted_count = result.rowcount
        
        db.session.commit()
        
        logger.info(f"✅ 成功删除 {deleted_count} 条已排产记录")
        
        return jsonify({
            'success': True,
            'message': f'成功删除 {deleted_count} 条记录',
            'deleted_count': deleted_count
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"❌ 删除已排产记录失败: {e}")
        return jsonify({
            'success': False,
            'message': f'删除失败: {str(e)}'
        }), 500

@done_lots_bp.route(get_api_route('production/save-and-publish-schedule'), methods=['POST'])
@login_required
def save_and_publish_schedule():
    """保存并发布排产结果"""
    try:
        data = request.get_json()
        schedule = data.get('schedule', [])
        metrics = data.get('metrics', {})
        publish_status = data.get('publish_status', 'DRAFT')
        
        if not schedule:
            return jsonify({
                'success': False,
                'message': '没有可保存的排产数据'
            }), 400
        
        saved_count = 0
        
        try:
            # 如果是发布状态，先清空现有数据
            if publish_status == 'PUBLISHED':
                delete_query = text("DELETE FROM lotprioritydone")
                db.session.execute(delete_query)
                logger.info("✅ 已清空现有排产数据，准备保存新数据")
            
            # 批量插入新数据
            for item in schedule:
                insert_query = text("""
                    INSERT INTO lotprioritydone (
                        PRIORITY, HANDLER_ID, LOT_ID, LOT_TYPE, GOOD_QTY,
                        PROD_ID, DEVICE, CHIP_ID, PKG_PN, PO_ID, STAGE,
                        WIP_STATE, PROC_STATE, HOLD_STATE, FLOW_ID, FLOW_VER,
                        RELEASE_TIME, FAC_ID, CREATE_TIME,
                        comprehensive_score, processing_time, changeover_time,
                        algorithm_version, match_type, priority_score
                    ) VALUES (
                        :priority, :handler_id, :lot_id, :lot_type, :good_qty,
                        :prod_id, :device, :chip_id, :pkg_pn, :po_id, :stage,
                        :wip_state, :proc_state, :hold_state, :flow_id, :flow_ver,
                        :release_time, :fac_id, NOW(),
                        :comprehensive_score, :processing_time, :changeover_time,
                        :algorithm_version, :match_type, :priority_score
                    )
                """)
                
                insert_data = {
                    'priority': item.get('PRIORITY') or saved_count + 1,
                    'handler_id': item.get('HANDLER_ID', ''),
                    'lot_id': item.get('LOT_ID', ''),
                    'lot_type': item.get('LOT_TYPE', ''),
                    'good_qty': item.get('GOOD_QTY', 0),
                    'prod_id': item.get('PROD_ID', ''),
                    'device': item.get('DEVICE', ''),
                    'chip_id': item.get('CHIP_ID', ''),
                    'pkg_pn': item.get('PKG_PN', ''),
                    'po_id': item.get('PO_ID', ''),
                    'stage': item.get('STAGE', ''),
                    'wip_state': item.get('WIP_STATE', ''),
                    'proc_state': item.get('PROC_STATE', ''),
                    'hold_state': item.get('HOLD_STATE', 0),
                    'flow_id': item.get('FLOW_ID', ''),
                    'flow_ver': item.get('FLOW_VER', ''),
                    'release_time': item.get('RELEASE_TIME') or None,
                    'fac_id': item.get('FAC_ID', ''),
                    'comprehensive_score': item.get('comprehensive_score', 0.0),
                    'processing_time': item.get('processing_time', 0.0),
                    'changeover_time': item.get('changeover_time', 0.0),
                    'algorithm_version': metrics.get('algorithm', 'enhanced_heuristic'),
                    'match_type': item.get('match_type', ''),
                    'priority_score': item.get('priority_score', 0.0)
                }
                
                db.session.execute(insert_query, insert_data)
                saved_count += 1
            
            # 提交事务
            db.session.commit()
            
            logger.info(f"✅ 成功保存并发布 {saved_count} 条排产记录")
            
            # Excel自动保存（如果启用）
            try:
                from app.services.excel_auto_save_service import get_excel_auto_save_service
                excel_service = get_excel_auto_save_service()
                
                if excel_service.is_auto_save_enabled():
                    # 构建用于Excel保存的数据
                    save_result = excel_service.auto_save_schedule_result(
                        schedule_data=schedule,
                        source='publish',
                        metrics=metrics
                    )
                    
                    if save_result.get('success'):
                        logger.info(f"✅ 保存发布排产结果已自动保存为Excel: {save_result.get('filename')} (共{save_result.get('records_count')}条记录)")
                    else:
                        logger.warning(f"⚠️ 保存发布排产结果Excel自动保存失败: {save_result.get('message')}")
                        
            except Exception as excel_error:
                logger.error(f"❌ 保存发布Excel自动保存异常: {excel_error}")
                # Excel保存失败不影响主要流程
            
            return jsonify({
                'success': True,
                'message': f'成功保存并发布 {saved_count} 条排产记录',
                'saved_count': saved_count,
                'publish_status': publish_status
            })
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"❌ 保存排产数据失败: {e}")
            return jsonify({
                'success': False,
                'message': f'保存失败: {str(e)}'
            }), 500
            
    except Exception as e:
        logger.error(f"❌ 保存并发布排产结果API异常: {e}")
        return jsonify({
            'success': False,
            'message': f'API异常: {str(e)}'
        }), 500

@done_lots_bp.route(get_api_route('production/get-failed-lots-from-logs'), methods=['GET'])
def get_failed_lots_from_logs():
    """从数据库中获取排产失败的批次信息（新的准确数据源）"""
    try:
        import os
        import re
        from datetime import datetime, timedelta
        from app.utils.db_connection_pool import get_db_connection_context
        
        # 🔥 获取查询参数
        current_only = request.args.get('current_only', 'false').lower() == 'true'
        hours_limit = int(request.args.get('hours', '24'))  # 默认24小时内的失败记录
        
        logger.info(f"🔍 开始从数据库中获取失败批次信息... (current_only={current_only}, hours_limit={hours_limit})")
        
        # 首先尝试从数据库获取失败批次信息
        try:
            logger.info("正在尝试获取数据库连接(连接池)...")
            with get_db_connection_context() as conn:
                logger.info("数据库连接成功，创建cursor...")
                cursor = conn.cursor()
            
            # 检查表是否存在
            logger.info("检查scheduling_failed_lots表是否存在...")
            check_table_sql = """
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_schema = DATABASE() 
            AND table_name = 'scheduling_failed_lots'
            """
            cursor.execute(check_table_sql)
            check_result = cursor.fetchone()
            logger.info(f"表检查结果: {check_result}")
            
            # 处理DictCursor返回的字典格式
            if isinstance(check_result, dict):
                table_exists = list(check_result.values())[0] > 0
            else:
                table_exists = check_result[0] > 0
            
            logger.info(f"scheduling_failed_lots表存在: {table_exists}")
            
            if table_exists:
                # 查询失败批次信息
                logger.info("开始查询失败批次数据...")
                
                # 🔥 根据参数决定查询范围 - 精简版：只获取关键字段
                if current_only:
                    # 🔧 修复：直接查询最近时间内的失败记录，避免子查询事务隔离问题
                    query_sql = """
                    SELECT
                        sfl.lot_id,
                        sfl.device,
                        sfl.stage,
                        sfl.good_qty,
                        sfl.failure_reason,
                        sfl.failure_details,
                        sfl.suggestion,
                        sfl.session_id,
                        sfl.timestamp,
                        -- 从ET_WAIT_LOT表获取关键字段
                        ewl.LOT_TYPE,
                        ewl.PKG_PN
                    FROM scheduling_failed_lots sfl
                    LEFT JOIN et_wait_lot ewl ON sfl.lot_id COLLATE utf8mb4_unicode_ci = ewl.LOT_ID COLLATE utf8mb4_unicode_ci
                    WHERE sfl.timestamp >= DATE_SUB(NOW(), INTERVAL %s HOUR)
                    ORDER BY sfl.timestamp DESC
                    LIMIT 1000
                    """
                    cursor.execute(query_sql, (hours_limit,))
                    logger.info(f"查询最近{hours_limit}小时内的失败记录（精简字段）")
                else:
                    # 获取所有失败记录，关联ET_WAIT_LOT表获取关键信息
                    query_sql = """
                    SELECT
                        sfl.lot_id,
                        sfl.device,
                        sfl.stage,
                        sfl.good_qty,
                        sfl.failure_reason,
                        sfl.failure_details,
                        sfl.suggestion,
                        sfl.session_id,
                        sfl.timestamp,
                        -- 从ET_WAIT_LOT表获取关键字段
                        ewl.LOT_TYPE,
                        ewl.PKG_PN
                    FROM scheduling_failed_lots sfl
                    LEFT JOIN et_wait_lot ewl ON sfl.lot_id COLLATE utf8mb4_unicode_ci = ewl.LOT_ID COLLATE utf8mb4_unicode_ci
                    ORDER BY sfl.timestamp DESC
                    LIMIT 1000
                    """
                    cursor.execute(query_sql)
                    logger.info("查询所有历史失败记录（精简字段）")
                
                results = cursor.fetchall()
                logger.info(f"查询到 {len(results)} 条失败批次记录")
                
                if not results:
                    logger.warning("未查询到失败批次数据")
                    return jsonify({
                        'success': True,
                        'data': {
                            'failed_lots': [],
                            'total_count': 0,
                            'summary': {
                                'total_failed': 0,
                                'config_missing': 0,
                                'equipment_incompatible': 0,
                                'other_reasons': 0
                            }
                        },
                        'message': '暂无失败批次数据'
                    })

                # 🔥 处理查询结果，生成精简的失败批次信息
                failed_lots = []
                summary_stats = {
                    'total_failed': 0,
                    'config_missing': 0,
                    'equipment_incompatible': 0,
                    'other_reasons': 0
                }
                
                for row in results:
                    lot_id = row['lot_id']
                    device = row['device'] or ''
                    stage = row['stage'] or ''
                    good_qty = row['good_qty'] or 0
                    failure_reason = row['failure_reason'] or '未知原因'
                    failure_details = row['failure_details'] or ''
                    # 🔥 优先使用数据库中的建议，如果没有则生成新的
                    suggestion = row.get('suggestion', '') or generate_suggestion(failure_reason, failure_details, device, stage)
                    session_id = row.get('session_id', '')
                    timestamp = row['timestamp']

                    # 🔥 获取ET_WAIT_LOT表的关键信息
                    lot_type = row.get('LOT_TYPE', '') or ''
                    pkg_pn = row.get('PKG_PN', '') or ''

                    # 调试日志 - 检查字段获取情况（只记录第一个批次）
                    if len(failed_lots) == 0:  # 第一个批次
                        logger.info(f"🔍 调试第一个批次 {lot_id}: row keys = {list(row.keys())}")
                        logger.info(f"🔍 调试第一个批次 {lot_id}: LOT_TYPE = '{row.get('LOT_TYPE')}', PKG_PN = '{row.get('PKG_PN')}'")
                        logger.info(f"🔍 调试第一个批次 {lot_id}: lot_type = '{lot_type}', pkg_pn = '{pkg_pn}'")
                        logger.info(f"🔍 调试第一个批次 {lot_id}: suggestion = '{suggestion}'")  # 🔥 新增：调试建议字段
                    
                    # 统计失败原因
                    summary_stats['total_failed'] += 1
                    if '配置' in failure_reason or 'config' in failure_reason.lower():
                        summary_stats['config_missing'] += 1
                    elif '设备' in failure_reason or '不兼容' in failure_reason:
                        summary_stats['equipment_incompatible'] += 1
                    else:
                        summary_stats['other_reasons'] += 1
                    
                    failure_info = {
                        # 🔥 精简的关键字段
                        'LOT_ID': lot_id,
                        'DEVICE': device,  # 产品名称
                        'STAGE': stage,    # 工序
                        'LOT_TYPE': lot_type,  # 工单分类
                        'PKG_PN': pkg_pn,      # 封装形式
                        'GOOD_QTY': good_qty,  # 良品数量
                        'failure_reason': failure_reason,  # 失败原因
                        'suggestion': suggestion,          # 建议解决方案
                        'timestamp': timestamp.isoformat() if timestamp else datetime.now().isoformat(),  # 创建时间
                        'session_id': session_id
                    }
                    failed_lots.append(failure_info)
                
                # 统计信息
                if current_only:
                    stats_sql = """
                    SELECT 
                        failure_reason,
                        COUNT(*) as count
                    FROM scheduling_failed_lots
                    WHERE timestamp >= NOW() - INTERVAL %s HOUR
                    GROUP BY failure_reason
                    ORDER BY count DESC
                    """
                    cursor.execute(stats_sql, (hours_limit,))
                else:
                    stats_sql = """
                    SELECT 
                        failure_reason,
                        COUNT(*) as count
                    FROM scheduling_failed_lots
                    GROUP BY failure_reason
                    ORDER BY count DESC
                    """
                    cursor.execute(stats_sql)
                failure_stats_rows = cursor.fetchall()
                failure_stats = [(row['failure_reason'], row['count']) for row in failure_stats_rows]
                
                cursor.close()
                logger.info(f"✅ 从数据库中获取到 {len(failed_lots)} 个失败批次")
                
                return jsonify({
                    'success': True,
                    'data': {
                        'failed_lots': failed_lots,
                        'total_count': len(failed_lots),
                        'summary': summary_stats
                    },
                    'debug_info': {
                        'data_source': 'database',
                        'table_name': 'scheduling_failed_lots',
                        'total_records': len(failed_lots),
                        'failure_statistics': {
                            reason: count for reason, count in failure_stats
                        }
                    }
                })
            else:
                logger.warning("⚠️ scheduling_failed_lots表不存在，回退到日志文件方式")
                cursor.close()
                raise Exception("scheduling_failed_lots表不存在")
                
        except Exception as db_error:
            logger.warning(f"⚠️ 数据库获取失败: {db_error}，回退到日志文件方式")
            
            # 回退到日志文件方式
            failed_lots = []
            log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'logs')
            
            # 查找最新的日志文件
            log_files = []
            if os.path.exists(log_dir):
                for filename in os.listdir(log_dir):
                    if filename.endswith('.log') and ('validation' in filename or 'app' in filename):
                        filepath = os.path.join(log_dir, filename)
                        if os.path.isfile(filepath):
                            log_files.append((filepath, os.path.getmtime(filepath)))
            
            # 按修改时间排序，获取最新的几个日志文件
            log_files.sort(key=lambda x: x[1], reverse=True)
            recent_files = log_files[:3]  # 最新的3个日志文件
            
            # 正则表达式匹配失败信息
            failure_patterns = [
                r'❌ 排产失败: ([A-Z0-9]+) - (.+)',
                r'⚠️ 批次 (\w+) 配置需求获取失败，跳过',
                r'批次 (\w+) 未找到匹配的测试规范 \(DEVICE=([^,]+), STAGE=([^)]+)\)',
                r'❌ 执行真实排产失败: (.+)',
                r'ERROR.*批次.*(\w+).*失败.*: (.+)'
            ]
            
            for filepath, _ in recent_files:
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                        
                        for line in lines:
                            # 匹配新的失败日志格式
                            match = re.search(r'❌ 排产失败: ([A-Z0-9]+) - (.+)', line)
                            if match:
                                lot_id, failure_reason = match.groups()
                                failed_lots.append({
                                    'LOT_ID': lot_id,
                                    'DEVICE': 'Unknown',
                                    'STAGE': 'Unknown',
                                    'GOOD_QTY': 0,
                                    'failure_reason': failure_reason,
                                    'suggestion': '请检查配置需求和设备状态'
                                })
                                continue
                            
                            # 匹配配置需求获取失败
                            match = re.search(r'⚠️ 批次 (\w+) 配置需求获取失败，跳过', line)
                            if match:
                                lot_id = match.group(1)
                                failed_lots.append({
                                    'LOT_ID': lot_id,
                                    'DEVICE': 'Unknown',
                                    'STAGE': 'Unknown',
                                    'GOOD_QTY': 0,
                                    'failure_reason': '配置需求获取失败，跳过',
                                    'suggestion': '请在ET_FT_TEST_SPEC表中补充配置信息'
                                })
                                continue
                            
                            # 匹配测试规范缺失
                            match = re.search(r'批次 (\w+) 未找到匹配的测试规范 \(DEVICE=([^,]+), STAGE=([^)]+)\)', line)
                            if match:
                                lot_id, device, stage = match.groups()
                                failed_lots.append({
                                    'LOT_ID': lot_id,
                                    'DEVICE': device,
                                    'STAGE': stage,
                                    'GOOD_QTY': 0,
                                    'failure_reason': f'未找到匹配的测试规范 (DEVICE={device}, STAGE={stage})',
                                    'suggestion': '请补充对应器件和工序的测试规范'
                                })
                                
                except Exception as e:
                    logger.warning(f"读取日志文件 {filepath} 失败: {e}")
                    continue
            
            # 去重
            unique_lots = {}
            for lot in failed_lots:
                lot_id = lot['LOT_ID']
                if lot_id not in unique_lots:
                    unique_lots[lot_id] = lot
            
            result_lots = list(unique_lots.values())
            
            # 统计信息
            log_files_count = len(recent_files)
            
            logger.info(f"✅ 从 {log_files_count} 个日志文件中提取到 {len(result_lots)} 个失败批次")
            
            return jsonify({
                'success': True,
                'data': {
                    'failed_lots': result_lots,
                    'total_count': len(result_lots),
                    'summary': {
                        'total_failed': len(result_lots),
                        'config_missing': 0,
                        'equipment_incompatible': 0,
                        'other_reasons': 0
                    }
                },
                'debug_info': {
                    'data_source': 'log_files_fallback',
                    'log_files_scanned': log_files_count,
                    'log_files_found': [os.path.basename(f) for f, _ in recent_files],
                    'unique_failures_found': len(result_lots),
                    'raw_failures_found': len(failed_lots)
                }
            })
        
    except Exception as e:
        logger.error(f"❌ 提取失败批次信息失败: {e}")
        return jsonify({
            'success': False,
            'message': f'提取失败: {str(e)}',
            'data': []
        }), 500 

@done_lots_bp.route(get_api_route('production/get-failed-lots-filter-options'), methods=['GET'])
def get_failed_lots_filter_options():
    """获取失败批次筛选选项的动态数据"""
    try:
        from app.utils.db_connection_pool import get_db_connection_context
        
        logger.info("🔍 获取失败批次筛选选项...")
        
        with get_db_connection_context() as conn:
            cursor = conn.cursor()
        
        # 检查表是否存在
        check_table_sql = """
        SELECT COUNT(*) 
        FROM information_schema.tables 
        WHERE table_schema = DATABASE() 
        AND table_name = 'scheduling_failed_lots'
        """
        cursor.execute(check_table_sql)
        check_result = cursor.fetchone()
        
        # 处理DictCursor返回的字典格式
        if isinstance(check_result, dict):
            table_exists = list(check_result.values())[0] > 0
        else:
            table_exists = check_result[0] > 0
        
        if table_exists:
            # 获取所有不同的工序
            stage_sql = """
            SELECT DISTINCT sfl.stage
            FROM scheduling_failed_lots sfl
            WHERE sfl.stage IS NOT NULL AND sfl.stage != ''
            ORDER BY sfl.stage
            """
            cursor.execute(stage_sql)
            stages = [row['stage'] if isinstance(row, dict) else row[0] for row in cursor.fetchall()]
            
            # 获取所有不同的工单分类（关联ET_WAIT_LOT表）
            lot_type_sql = """
            SELECT DISTINCT ewl.LOT_TYPE
            FROM scheduling_failed_lots sfl
            LEFT JOIN et_wait_lot ewl ON sfl.lot_id COLLATE utf8mb4_unicode_ci = ewl.LOT_ID COLLATE utf8mb4_unicode_ci
            WHERE ewl.LOT_TYPE IS NOT NULL AND ewl.LOT_TYPE != ''
            ORDER BY ewl.LOT_TYPE
            """
            cursor.execute(lot_type_sql)
            lot_types = [row['LOT_TYPE'] if isinstance(row, dict) else row[0] for row in cursor.fetchall()]
            
            # 获取所有不同的失败原因
            failure_reason_sql = """
            SELECT DISTINCT sfl.failure_reason
            FROM scheduling_failed_lots sfl
            WHERE sfl.failure_reason IS NOT NULL AND sfl.failure_reason != ''
            ORDER BY sfl.failure_reason
            """
            cursor.execute(failure_reason_sql)
            failure_reasons = [row['failure_reason'] if isinstance(row, dict) else row[0] for row in cursor.fetchall()]
            
            cursor.close()
            # 分析失败原因并生成分类选项
            failure_types = []
            config_keywords = ['配置', '规范', 'config', 'spec']
            equipment_keywords = ['设备', '不兼容', 'equipment', 'incompatible']
            
            has_config_issues = any(any(keyword in reason for keyword in config_keywords) for reason in failure_reasons)
            has_equipment_issues = any(any(keyword in reason for keyword in equipment_keywords) for reason in failure_reasons)
            has_other_issues = len(failure_reasons) > 0
            
            if has_config_issues:
                failure_types.append({'value': '配置', 'label': '配置缺失'})
            if has_equipment_issues:
                failure_types.append({'value': '设备', 'label': '设备不兼容'})
            if has_other_issues:
                failure_types.append({'value': '其他', 'label': '其他原因'})
            
            return jsonify({
                'success': True,
                'data': {
                    'stages': [{'value': stage, 'label': stage} for stage in stages],
                    'lot_types': [{'value': lot_type, 'label': lot_type} for lot_type in lot_types],
                    'failure_types': failure_types,
                    'failure_reasons': failure_reasons
                },
                'message': '成功获取筛选选项'
            })
        else:
            # 表不存在时返回默认选项
            return jsonify({
                'success': True,
                'data': {
                    'stages': [],
                    'lot_types': [],
                    'failure_types': [
                        {'value': '配置', 'label': '配置缺失'},
                        {'value': '设备', 'label': '设备不兼容'},
                        {'value': '其他', 'label': '其他原因'}
                    ],
                    'failure_reasons': []
                },
                'message': '数据表不存在，返回默认选项'
            })
            
    except Exception as e:
        logger.error(f"❌ 获取筛选选项失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取筛选选项失败: {str(e)}'
        }), 500 

@done_lots_bp.route(get_api_route('production/clear-failed-lots'), methods=['POST'])
@login_required
def clear_failed_lots():
    """
    清空排产失败记录 - 优化的死锁重试机制
    """
    import time
    max_retries = 2  # 优化：减少重试次数 3→2
    retry_delay = 0.1  # 固定100ms延迟，移除指数退避
    
    for attempt in range(max_retries):
        try:
            logger.info(f"[DEBUG] 开始清空排产失败记录... (尝试 {attempt + 1}/{max_retries})")
            
            # 获取MySQL连接
            from app.utils.db_connection_pool import get_db_connection_context
            
            with get_db_connection_context() as conn:
                # 优化：缩短锁等待超时时间 5→3秒
                conn.cursor().execute("SET SESSION innodb_lock_wait_timeout = 3")
                cursor = conn.cursor()
                
                try:
                    # 检查表是否存在
                    check_table_sql = """
                    SELECT COUNT(*) 
                    FROM information_schema.tables 
                    WHERE table_schema = DATABASE() 
                    AND table_name = 'scheduling_failed_lots'
                    """
                    cursor.execute(check_table_sql)
                    check_result = cursor.fetchone()
                    
                    # 处理DictCursor返回的字典格式
                    if isinstance(check_result, dict):
                        table_exists = list(check_result.values())[0] > 0
                    else:
                        table_exists = check_result[0] > 0
                    
                    if not table_exists:
                        return jsonify({
                            'success': False,
                            'message': 'scheduling_failed_lots表不存在'
                        }), 400
                    
                    # 获取删除前的记录数
                    count_sql = "SELECT COUNT(*) FROM scheduling_failed_lots"
                    cursor.execute(count_sql)
                    count_result = cursor.fetchone()
                    
                    if isinstance(count_result, dict):
                        before_count = list(count_result.values())[0]
                    else:
                        before_count = count_result[0]
                    
                    logger.info(f"[DEBUG] 准备删除 {before_count} 条失败记录")
                    
                    # 如果没有记录，直接返回成功
                    if before_count == 0:
                        logger.info("[INFO] 没有失败记录需要清空")
                        return jsonify({
                            'success': True,
                            'message': '没有失败记录需要清空',
                            'deleted_count': 0
                        })
                    
                    # 优化：简化事务，移除ALTER TABLE操作
                    conn.begin()
                    try:
                        delete_sql = "DELETE FROM scheduling_failed_lots"
                        cursor.execute(delete_sql)
                        
                        # 提交事务
                        conn.commit()
                        logger.info(f"[OK] 成功清空排产失败记录，删除了 {before_count} 条记录")
                        
                        return jsonify({
                            'success': True,
                            'message': f'成功清空排产失败记录，删除了 {before_count} 条记录',
                            'deleted_count': before_count
                        })
                        
                    except Exception as tx_e:
                        conn.rollback()
                        raise tx_e
                        
                finally:
                    # 确保cursor关闭
                    cursor.close()
                    
        except Exception as e:
            error_msg = str(e)
            logger.error(f"[X] 清空排产失败记录失败 (尝试 {attempt + 1}): {error_msg}")
            
            # 检查是否为死锁或锁等待超时错误
            if "Deadlock found" in error_msg or "Lock wait timeout" in error_msg:
                if attempt < max_retries - 1:  # 还有重试机会
                    logger.warning(f"[WARNING] 检测到锁冲突，{retry_delay}秒后重试...")
                    time.sleep(retry_delay)
                    # 优化：使用固定延迟，不再使用指数退避
                    continue
                else:
                    logger.error(f"[ERROR] 重试{max_retries}次后仍然失败")
                    return jsonify({
                        'success': False,
                        'message': f'清空失败: 数据库锁冲突，重试{max_retries}次后仍然失败。请稍后再试或联系管理员'
                    }), 500
            else:
                # 非锁冲突错误，直接返回
                return jsonify({
                    'success': False,
                    'message': f'清空失败: {error_msg}'
                }), 500
    
    # 理论上不会执行到这里
    return jsonify({
        'success': False,
        'message': '清空失败: 未知错误'
    }), 500

@done_lots_bp.route(get_api_route('production/export-schedule-to-path'), methods=['POST'])
@login_required
def export_schedule_to_path():
    """导出排产结果到指定路径的Excel文件"""
    try:
        data = request.get_json()
        export_path = data.get('export_path', '')
        
        if not export_path:
            return jsonify({
                'success': False,
                'message': '请指定导出路径'
            }), 400
        
        import os
        import pandas as pd
        from datetime import datetime
        
        # 验证路径是否存在
        export_dir = os.path.dirname(export_path)
        if not os.path.exists(export_dir):
            try:
                os.makedirs(export_dir)
                logger.info(f"创建导出目录: {export_dir}")
            except Exception as e:
                return jsonify({
                    'success': False,
                    'message': f'无法创建导出目录: {str(e)}'
                }), 400
        
        # 获取所有lotprioritydone数据
        query = text("""
            SELECT 
                PRIORITY, LOT_ID, DEVICE, CHIP_ID, HANDLER_ID, GOOD_QTY,
                comprehensive_score, STAGE, STEP, WIP_STATE, CREATE_TIME
            FROM lotprioritydone 
            ORDER BY PRIORITY ASC, CREATE_TIME DESC
        """)
        
        result = db.session.execute(query)
        
        # 构建导出数据（与exportData()保持一致的字段名称）
        export_data = []
        for row in result.fetchall():
            export_data.append({
                '优先级': row[0] or '',
                '内部工单号': row[1] or '',
                '产品名称': row[2] or '',
                '芯片名称': row[3] or '',
                '分选机ID': row[4] or '',
                '良品数量': row[5] or 0,
                '综合评分': row[6] or 0.0,
                '工序': row[7] or '',
                '工步': row[8] or '',
                '状态': row[9] or '',
                '创建时间': row[10] or ''
            })
        
        if not export_data:
            return jsonify({
                'success': False,
                'message': '没有数据可以导出'
            }), 400
        
        # 创建DataFrame并导出到Excel
        df = pd.DataFrame(export_data)
        
        # 确保文件扩展名为.xlsx
        if not export_path.lower().endswith('.xlsx'):
            export_path = export_path + '.xlsx'
        
        with pd.ExcelWriter(export_path, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='已排产批次', index=False)
            
            # 调整列宽以适应内容
            worksheet = writer.sheets['已排产批次']
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)  # 最大宽度50
                worksheet.column_dimensions[column_letter].width = adjusted_width
        
        logger.info(f"✅ 成功导出 {len(export_data)} 条排产记录到: {export_path}")
        
        return jsonify({
            'success': True,
            'message': f'成功导出 {len(export_data)} 条记录到指定路径',
            'export_path': export_path,
            'records_count': len(export_data)
        })
        
    except Exception as e:
        logger.error(f"❌ 导出排产结果到指定路径失败: {e}")
        return jsonify({
            'success': False,
            'message': f'导出失败: {str(e)}'
        }), 500 