{% extends "resources/base_resource.html" %}

{% set page_title = "待排产批次" %}
{% set table_title = "待排产批次" %}
{% set page_description = "管理待排产批次信息，包含内部工单号、产品名称、工序、数量、优先级等。支持筛选、编辑、删除等操作。" %}
{% set table_name = "et_wait_lot" %}
{% set api_endpoint = "/api/v2/resources" %}

{% block extra_css %}
{{ super() }}
<style>
/* 待排产批次专用样式 */
.status-waiting { background-color: #fff3cd; color: #856404; }
.status-processing { background-color: #d1ecf1; color: #0c5460; }
.status-assigned { background-color: #d4edda; color: #155724; }

.priority-high { font-weight: bold; color: #dc3545; }
.priority-medium { font-weight: bold; color: #fd7e14; }
.priority-low { color: #6c757d; }

/* 内部工单号列样式 */
.lot-id-column {
    font-family: 'Monaco', 'Consolas', monospace;
    font-weight: bold;
}

/* 数量列右对齐 */
.quantity-column {
    text-align: right;
}

/* Excel上传按钮 */
.excel-upload-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    transition: all 0.2s;
}

.excel-upload-btn:hover {
    background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
    transform: translateY(-1px);
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="card-title mb-0">{{ page_title }}</h5>
                        <div>
                            <button type="button" class="btn excel-upload-btn me-2" onclick="showExcelUploadModal()">
                                <i class="fas fa-file-excel me-1"></i>Excel导入
                            </button>
                            <button type="button" class="btn btn-success me-2" onclick="addRecord()">
                                <i class="fas fa-plus me-1"></i>新增
                            </button>
                            <button type="button" class="btn btn-outline-primary me-2" onclick="refreshData()">
                                <i class="fas fa-sync-alt me-1"></i>刷新
                            </button>
                            <button type="button" class="btn btn-success me-2" onclick="exportData()" title="导出当前页面所有待排产批次数据">
                                <i class="fas fa-file-excel me-1"></i>导出Excel
                            </button>
                            <button type="button" class="btn btn-warning me-2" onclick="moveToScheduled()">
                                <i class="fas fa-arrow-right me-1"></i>移至已排产
                            </button>
                        </div>
                    </div>
                    
                    <!-- 高级筛选面板 -->
                    <div class="mb-3">
                        <h6>
                            <i class="fas fa-filter me-2"></i>高级筛选
                            <small class="text-muted ms-2">支持多条件组合查询</small>
                        </h6>
                        
                        <!-- 筛选条件 -->
                        <div id="filterConditions">
                            <div class="filter-row" data-index="0">
                                <div class="filter-field">
                                    <label class="form-label form-label-sm">字段</label>
                                    <select class="form-select form-select-sm" name="field">
                                        <option value="">请选择字段</option>
                                    </select>
                                </div>
                                <div class="filter-operator">
                                    <label class="form-label form-label-sm">操作符</label>
                                    <select class="form-select form-select-sm" name="operator">
                                        <option value="contains">包含</option>
                                        <option value="equals">等于</option>
                                        <option value="starts_with">开始于</option>
                                        <option value="ends_with">结束于</option>
                                        <option value="not_equals">不等于</option>
                                    </select>
                                </div>
                                <div class="filter-value">
                                    <label class="form-label form-label-sm">值</label>
                                    <input type="text" class="form-control form-control-sm" name="value" placeholder="输入筛选值">
                                </div>
                                <div class="filter-actions">
                                    <label class="form-label form-label-sm">&nbsp;</label>
                                    <div>
                                        <button type="button" class="btn btn-sm btn-success" onclick="addFilterCondition()" title="添加条件">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-danger" onclick="removeFilterCondition(0)" title="删除条件">
                                            <i class="fas fa-minus"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 筛选操作按钮 -->
                        <div class="d-flex justify-content-between align-items-center mt-2">
                            <div>
                                <button type="button" class="btn btn-primary btn-sm" onclick="applyFilter()">
                                    <i class="fas fa-search me-1"></i>应用筛选
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm ms-2" onclick="clearFilter()">
                                    <i class="fas fa-times me-1"></i>清除筛选
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 数据预览区域 -->
                    <div class="preview-area">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">{{ table_title }}数据预览</h6>
                            <div>
                                <span class="badge bg-primary me-2" id="recordCount">0 条记录</span>
                                <select class="form-select form-select-sm d-inline-block" style="width: auto;" id="pageSize" onchange="changePageSize()">
                                    <option value="25">25 条/页</option>
                                    <option value="50" selected>50 条/页</option>
                                    <option value="100">100 条/页</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- 批量操作工具栏 -->
                        <div class="batch-operations mb-3" style="display: none;" id="batchOperations">
                            <div class="alert alert-info py-2">
                                <span id="selectedCount">0</span> 条记录已选择
                                <button type="button" class="btn btn-sm btn-outline-danger ms-3" onclick="batchDelete()">
                                    <i class="fas fa-trash me-1"></i>批量删除
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-warning ms-2" onclick="batchMoveToScheduled()">
                                    <i class="fas fa-arrow-right me-1"></i>批量移至已排产
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary ms-2" onclick="clearSelection()">
                                    <i class="fas fa-times me-1"></i>取消选择
                                </button>
                            </div>
                        </div>
                        
                        <div class="table-responsive" style="position: relative;">
                            <div class="loading-overlay" id="loadingOverlay" style="display: none;">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                            </div>
                            
                            <table class="table table-sm table-hover table-striped" id="dataTable">
                                <thead class="table-light">
                                    <tr id="tableHeaders">
                                        <!-- 表头将动态生成 -->
                                    </tr>
                                </thead>
                                <tbody id="tableBody">
                                    <tr>
                                        <td colspan="20" class="text-center">加载中...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 分页导航 -->
                        <nav class="mt-3" aria-label="数据分页">
                            <ul class="pagination justify-content-center" id="pagination">
                                <!-- 分页按钮将动态生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{{ super() }}
<!-- Excel导出库 -->
<script src="{{ url_for('static', filename='js/libs/xlsx.full.min.js') }}"></script>
<script>
// 页面配置
const API_ENDPOINT = '{{ api_endpoint }}';
const TABLE_NAME = '{{ table_name }}';

// 移至已排产功能
function moveToScheduled() {
    if (confirm('确定要将选中的批次移至已排产吗？')) {
        // 获取选中的记录
        const selectedRows = document.querySelectorAll('#dataTable tbody input[type="checkbox"]:checked');
        if (selectedRows.length === 0) {
            alert('请选择要移动的批次');
            return;
        }
        
        const selectedIds = Array.from(selectedRows).map(row => 
            parseInt(row.closest('tr').dataset.id)
        );
        
        moveLotsToScheduled(selectedIds);
    }
}

function batchMoveToScheduled() {
    const selectedIds = getSelectedRowIds();
    if (selectedIds.length === 0) {
        alert('请选择要移动的批次');
        return;
    }
    
    if (confirm(`确定要将选中的 ${selectedIds.length} 个批次移至已排产吗？`)) {
        moveLotsToScheduled(selectedIds);
    }
}

function moveLotsToScheduled(ids) {
    // 显示加载状态
    showLoading();
    
    // 调用API将批次移至已排产
    fetch('/api/v2/production/wait-lots/move-to-scheduled', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ ids: ids }),
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(result => {
        hideLoading();
        
        if (result.success) {
            showNotification('success', `成功移动 ${result.moved_count} 个批次至已排产`);
            loadData(); // 刷新数据
            clearSelection();
        } else {
            showNotification('error', result.error || '移动失败');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('移动批次失败:', error);
        showNotification('error', '移动失败: ' + error.message);
    });
}

// 自定义数据格式化函数
function formatCellValue(value, column) {
    if (column === 'status') {
        const statusClass = value ? `status-${value.toLowerCase()}` : '';
        return `<span class="badge ${statusClass}">${value || ''}</span>`;
    }
    
    if (column === 'priority') {
        let priorityClass = 'priority-low';
        if (value >= 8) priorityClass = 'priority-high';
        else if (value >= 5) priorityClass = 'priority-medium';
        
        return `<span class="${priorityClass}">${value || ''}</span>`;
    }
    
    if (column === 'lot_id') {
        return `<span class="lot-id-column">${value || ''}</span>`;
    }
    
    if (column === 'quantity') {
        return `<span class="quantity-column">${value || ''}</span>`;
    }
    
    // 默认格式化
    return value || '';
}

// Excel上传相关函数
function showExcelUploadModal() {
    const modal = new bootstrap.Modal(document.getElementById('excelUploadModal'));
    modal.show();

    // 重置表单
    document.getElementById('excelFiles').value = '';
    document.getElementById('uploadProgress').style.display = 'none';
    document.getElementById('uploadResult').style.display = 'none';
}

function uploadExcelFiles() {
    const fileInput = document.getElementById('excelFiles');
    const files = fileInput.files;

    if (files.length === 0) {
        alert('请选择要上传的Excel文件');
        return;
    }

    // 验证文件名是否包含"wait"或"lot"关键字
    const file = files[0];
    const fileName = file.name.toLowerCase();
    if (!fileName.includes('wait') && !fileName.includes('lot')) {
        alert('文件名必须包含"wait"或"lot"关键字，请重新选择正确的待排产批次文件');
        return;
    }

    const formData = new FormData();
    formData.append('files', file);

    // 显示进度
    document.getElementById('uploadProgress').style.display = 'block';
    document.getElementById('uploadStatus').textContent = '正在上传...';

    fetch('/api/v2/production/wait-lots/upload', {
        method: 'POST',
        body: formData,
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(result => {
        document.getElementById('uploadProgress').style.display = 'none';
        document.getElementById('uploadResult').style.display = 'block';

        if (result.success) {
            document.getElementById('uploadResult').innerHTML = `
                <div class="alert alert-success">
                    <h6>上传成功！</h6>
                    <p>成功导入 ${result.total_processed} 条记录</p>
                </div>
            `;

            // 刷新数据
            setTimeout(() => {
                loadData();
                bootstrap.Modal.getInstance(document.getElementById('excelUploadModal')).hide();
            }, 2000);
        } else {
            document.getElementById('uploadResult').innerHTML = `
                <div class="alert alert-danger">
                    <h6>上传失败</h6>
                    <p>${result.message || '未知错误'}</p>
                </div>
            `;
        }
    })
    .catch(error => {
        document.getElementById('uploadProgress').style.display = 'none';
        document.getElementById('uploadResult').style.display = 'block';
        document.getElementById('uploadResult').innerHTML = `
            <div class="alert alert-danger">
                <h6>上传失败</h6>
                <p>网络错误: ${error.message}</p>
            </div>
        `;
    });
}

// 导出待排产批次数据
function exportData() {
    if (!window.tableData || window.tableData.length === 0) {
        alert('没有数据可导出');
        return;
    }
    
    try {
        // 准备导出数据 - 使用与其他页面一致的格式
        const exportData = window.tableData.map((item, index) => ({
            '序号': index + 1,
            '内部工单号': item.LOT_ID || '',
            '产品名称': item.DEVICE || '',
            '工序': item.STAGE || '',
            '封装': item.PKG_PN || '',
            '芯片名称': item.CHIP_ID || '',
            '数量': item.GOOD_QTY || 0,
            '创建时间': item.CREATE_TIME || '',
            '更新时间': item.UPDATE_TIME || '',
            '备注': item.REMARK || ''
        }));
        
        // 使用XLSX导出Excel
        const ws = XLSX.utils.json_to_sheet(exportData);
        
        // 设置列宽
        ws['!cols'] = [
            { width: 8 },   // 序号
            { width: 25 },  // 内部工单号
            { width: 20 },  // 产品名称
            { width: 15 },  // 工序
            { width: 15 },  // 封装
            { width: 20 },  // 芯片名称
            { width: 10 },  // 数量
            { width: 20 },  // 创建时间
            { width: 20 },  // 更新时间
            { width: 30 }   // 备注
        ];
        
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, '待排产批次');
        
        // 生成文件名
        const now = new Date();
        const timestamp = now.getFullYear() + 
            String(now.getMonth() + 1).padStart(2, '0') + 
            String(now.getDate()).padStart(2, '0') + '_' +
            String(now.getHours()).padStart(2, '0') + 
            String(now.getMinutes()).padStart(2, '0');
        const filename = `待排产批次_${timestamp}.xlsx`;
        
        XLSX.writeFile(wb, filename);
        
        // 显示成功提示
        console.log(`✅ 导出成功: ${filename}`);
        alert(`导出成功！\n文件名: ${filename}\n数据量: ${exportData.length} 条记录`);
        
    } catch (error) {
        console.error('❌ 导出失败:', error);
        alert('导出失败: ' + error.message);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadColumns();
    loadData();
});
</script>

<!-- Excel上传模态框 -->
<div class="modal fade" id="excelUploadModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-file-excel me-2"></i>导入待排产批次Excel文件
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="excelFiles" class="form-label">选择Excel文件</label>
                    <input type="file" class="form-control" id="excelFiles" accept=".xlsx,.xls" multiple>
                    <div class="form-text">支持.xlsx和.xls格式，文件名应包含"wait"或"lot"关键字</div>
                </div>

                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-1"></i>待排产批次文件格式说明</h6>
                    <p><strong>必需字段：</strong>LOT_ID(内部工单号), DEVICE(产品名称), GOOD_QTY(数量)</p>
                    <p><strong>可选字段：</strong>STAGE(工序), PKG_PN(封装), CHIP_ID(芯片名称), CREATE_TIME(创建时间)</p>
                    <p><strong>文件命名：</strong>文件名包含"wait"或"lot"关键字即可识别为待排产批次文件</p>
                </div>

                <div class="upload-progress" id="uploadProgress" style="display: none;">
                    <div class="progress">
                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                    <small class="text-muted" id="uploadStatus">准备上传...</small>
                </div>

                <div id="uploadResult" style="display: none;"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="uploadExcelFiles()">
                    <i class="fas fa-upload me-1"></i>开始导入
                </button>
            </div>
        </div>
    </div>
</div>

{% endblock %}